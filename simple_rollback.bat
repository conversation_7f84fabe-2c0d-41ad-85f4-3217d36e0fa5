@echo off
echo Starting rollback process...
echo.

set BACKUP_PATH=D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1
set CURRENT_DIR=%~dp0
set SAFETY_DIR=%CURRENT_DIR%backup_%date:~0,4%%date:~5,2%%date:~8,2%

echo Backup path: %BACKUP_PATH%
echo Current dir: %CURRENT_DIR%
echo Safety dir: %SAFETY_DIR%
echo.

echo Step 1: Check if backup exists
if not exist "%BACKUP_PATH%" (
    echo ERROR: Backup folder not found!
    pause
    exit /b 1
)
echo Backup folder found!
echo.

echo Step 2: Create safety backup
mkdir "%SAFETY_DIR%" 2>nul
echo Safety backup folder created.
echo.

echo Step 3: Backup current files
xcopy "%CURRENT_DIR%*" "%SAFETY_DIR%\" /e /h /y /exclude:simple_rollback.bat >nul 2>&1
echo Current files backed up.
echo.

echo Step 4: Clear current directory (keeping this script)
for /f "delims=" %%i in ('dir /b /a-d "%CURRENT_DIR%"') do (
    if not "%%i"=="simple_rollback.bat" (
        del /q "%CURRENT_DIR%%%i" >nul 2>&1
    )
)

for /f "delims=" %%i in ('dir /b /ad "%CURRENT_DIR%"') do (
    if not "%%~nxi"=="backup_%date:~0,4%%date:~5,2%%date:~8,2%" (
        rmdir /s /q "%CURRENT_DIR%%%i" >nul 2>&1
    )
)
echo Current directory cleared.
echo.

echo Step 5: Copy files from backup
xcopy "%BACKUP_PATH%\*" "%CURRENT_DIR%" /e /h /y >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy from backup!
    echo Restoring from safety backup...
    xcopy "%SAFETY_DIR%\*" "%CURRENT_DIR%" /e /h /y >nul 2>&1
    pause
    exit /b 1
)
echo Files copied from backup successfully!
echo.

echo Step 6: Start project
if exist "启动项目.bat" (
    echo Starting with 启动项目.bat
    start "" "启动项目.bat"
) else if exist "index-standalone.html" (
    echo Opening static version
    start "" "index-standalone.html"
) else if exist "index.html" (
    echo Opening index.html
    start "" "index.html"
) else (
    echo No startup file found
)

echo.
echo Rollback completed!
echo Safety backup saved to: %SAFETY_DIR%
echo.
pause
