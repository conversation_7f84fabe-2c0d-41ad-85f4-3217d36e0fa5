@echo off
echo ================================
echo BitBear System Manual Rollback
echo ================================
echo.

set "CURRENT_DIR=%~dp0"

echo Please enter the full path to your backup folder:
echo Example: D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1 文件
echo.
set /p "BACKUP_PATH=Backup path: "

if "%BACKUP_PATH%"=="" (
    echo No path entered. Exiting...
    pause
    exit /b 1
)

echo.
echo Checking backup path: %BACKUP_PATH%

if not exist "%BACKUP_PATH%" (
    echo Error: Backup path does not exist!
    echo Please check the path and try again.
    pause
    exit /b 1
)

echo Backup path found!
echo.

echo Listing backup contents:
dir "%BACKUP_PATH%" /b
echo.

echo Do you want to proceed with the rollback? (Y/N)
set /p "CONFIRM=Confirm: "

if /i not "%CONFIRM%"=="Y" (
    echo Rollback cancelled.
    pause
    exit /b 0
)

echo.
echo Creating safety backup of current version...
set "TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "TIMESTAMP=%TIMESTAMP: =0%"
set "SAFETY_BACKUP=%CURRENT_DIR%current_backup_%TIMESTAMP%"

mkdir "%SAFETY_BACKUP%" 2>nul

echo Backing up current files...
for %%f in ("%CURRENT_DIR%*") do (
    if not "%%~nxf"=="%~nx0" (
        if not "%%~nxf"=="current_backup_%TIMESTAMP%" (
            xcopy "%%f" "%SAFETY_BACKUP%\" /e /h /y >nul 2>&1
        )
    )
)

echo Clearing current directory...
for /f "delims=" %%i in ('dir /b /a-d "%CURRENT_DIR%"') do (
    if not "%%i"=="%~nx0" (
        del /q "%CURRENT_DIR%%%i" >nul 2>&1
    )
)

for /f "delims=" %%i in ('dir /b /ad "%CURRENT_DIR%"') do (
    if not "%%~nxi"=="current_backup_%TIMESTAMP%" (
        rmdir /s /q "%CURRENT_DIR%%%i" >nul 2>&1
    )
)

echo Restoring from backup...
xcopy "%BACKUP_PATH%\*" "%CURRENT_DIR%" /e /h /y >nul 2>&1

if %errorlevel% neq 0 (
    echo Error: Restore failed! Rolling back...
    xcopy "%SAFETY_BACKUP%\*" "%CURRENT_DIR%" /e /h /y >nul 2>&1
    echo Rollback to original state completed.
    pause
    exit /b 1
)

echo.
echo ================================
echo Rollback successful!
echo ================================
echo.

echo Current version backed up to: %SAFETY_BACKUP%
echo.

echo Starting project...
echo.

REM Try different startup methods
if exist "启动项目.bat" (
    echo Found startup script: 启动项目.bat
    start "" "启动项目.bat"
    goto :startup_done
)

if exist "start_server.bat" (
    echo Found startup script: start_server.bat
    start "" "start_server.bat"
    goto :startup_done
)

if exist "快速启动后台.bat" (
    echo Found startup script: 快速启动后台.bat
    start "" "快速启动后台.bat"
    goto :startup_done
)

if exist "启动PHP后台服务器.bat" (
    echo Found startup script: 启动PHP后台服务器.bat
    start "" "启动PHP后台服务器.bat"
    goto :startup_done
)

echo No startup script found. Trying manual startup...

REM Check PHP
php --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Starting PHP development server...
    start "BitBear PHP Server" cmd /k "echo Starting PHP server on localhost:8000 && php -S localhost:8000 -t . && pause"
    goto :startup_done
)

REM Check Python
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Starting Python HTTP server...
    start "BitBear Python Server" cmd /k "echo Starting Python server on localhost:8000 && python -m http.server 8000 && pause"
    goto :startup_done
)

echo Neither PHP nor Python found.
echo Please install PHP or Python to run the development server.
echo Or run the appropriate startup script manually.

:startup_done
echo.
echo Waiting for server to start...
timeout /t 5 >nul

echo Opening browser...
start http://localhost:8000 >nul 2>&1

echo.
echo ================================
echo Setup Complete!
echo ================================
echo.
echo The project should now be running at:
echo http://localhost:8000
echo.
echo If the browser didn't open automatically,
echo please visit the URL manually.
echo.
echo Current version backup: %SAFETY_BACKUP%
echo.
pause
