# BitBear System Version Rollback Script
Write-Host "================================" -ForegroundColor Green
Write-Host "BitBear System Version Rollback" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

$currentDir = Get-Location
$backupPath = "D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1"

Write-Host "Current directory: $currentDir" -ForegroundColor Cyan
Write-Host "Backup path: $backupPath" -ForegroundColor Cyan
Write-Host ""

# Verify backup folder exists
Write-Host "Verifying backup folder..." -ForegroundColor Yellow
if (-not (Test-Path $backupPath)) {
    Write-Host "Error: Backup folder not found!" -ForegroundColor Red
    Write-Host "Path: $backupPath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Backup folder verified successfully!" -ForegroundColor Green
Write-Host ""

# Show backup contents
Write-Host "Backup folder contents:" -ForegroundColor Yellow
Get-ChildItem $backupPath | Select-Object Name, LastWriteTime | Format-Table -AutoSize
Write-Host ""

# Confirm operation
Write-Host "WARNING: This will replace all current files with the backup version." -ForegroundColor Red
Write-Host "Current files will be backed up to a safety folder." -ForegroundColor Yellow
Write-Host ""
$confirm = Read-Host "Do you want to proceed? (Y/N)"

if ($confirm -ne "Y" -and $confirm -ne "y") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 0
}

Write-Host ""
Write-Host "Creating safety backup of current version..." -ForegroundColor Yellow

# Create timestamp for safety backup
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$safetyBackup = Join-Path $currentDir "current_backup_$timestamp"

try {
    New-Item -ItemType Directory -Path $safetyBackup -Force | Out-Null
    Write-Host "Safety backup folder created: $safetyBackup" -ForegroundColor Green
    
    # Backup current files (exclude this script and backup folders)
    Write-Host "Backing up current files..." -ForegroundColor Yellow
    $items = Get-ChildItem $currentDir -Force | Where-Object { 
        $_.Name -ne "rollback.ps1" -and 
        $_.Name -notlike "current_backup_*" -and
        $_.Name -ne "execute_rollback.bat" -and
        $_.Name -ne "manual_rollback.bat" -and
        $_.Name -ne "start_project.bat"
    }
    
    foreach ($item in $items) {
        $destPath = Join-Path $safetyBackup $item.Name
        if ($item.PSIsContainer) {
            Copy-Item $item.FullName $destPath -Recurse -Force
        } else {
            Copy-Item $item.FullName $destPath -Force
        }
    }
    
    Write-Host "Current files backed up successfully!" -ForegroundColor Green
    Write-Host ""
    
    # Clear current directory
    Write-Host "Clearing current directory..." -ForegroundColor Yellow
    foreach ($item in $items) {
        Remove-Item $item.FullName -Recurse -Force
    }
    Write-Host "Current directory cleared!" -ForegroundColor Green
    Write-Host ""
    
    # Restore from backup
    Write-Host "Restoring files from backup..." -ForegroundColor Yellow
    $backupItems = Get-ChildItem $backupPath -Force
    
    foreach ($item in $backupItems) {
        $destPath = Join-Path $currentDir $item.Name
        if ($item.PSIsContainer) {
            Copy-Item $item.FullName $destPath -Recurse -Force
        } else {
            Copy-Item $item.FullName $destPath -Force
        }
    }
    
    Write-Host ""
    Write-Host "================================" -ForegroundColor Green
    Write-Host "Rollback completed successfully!" -ForegroundColor Green
    Write-Host "================================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Files restored from: $backupPath" -ForegroundColor Cyan
    Write-Host "Current version backed up to: $safetyBackup" -ForegroundColor Cyan
    Write-Host ""
    
    # Start the project
    Write-Host "Starting project..." -ForegroundColor Yellow
    Write-Host ""
    
    # Try different startup methods
    if (Test-Path "启动项目.bat") {
        Write-Host "Found startup script: 启动项目.bat" -ForegroundColor Green
        Write-Host "Starting project with existing script..." -ForegroundColor Yellow
        Start-Process "启动项目.bat"
    }
    elseif (Test-Path "start_server.bat") {
        Write-Host "Found startup script: start_server.bat" -ForegroundColor Green
        Start-Process "start_server.bat"
    }
    elseif (Test-Path "快速启动后台.bat") {
        Write-Host "Found startup script: 快速启动后台.bat" -ForegroundColor Green
        Start-Process "快速启动后台.bat"
    }
    else {
        Write-Host "No startup script found. Attempting manual startup..." -ForegroundColor Yellow
        
        # Check for PHP
        try {
            $phpVersion = php --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "PHP found! Starting PHP development server..." -ForegroundColor Green
                Start-Process cmd -ArgumentList "/k", "echo BitBear System - PHP Server && echo Server: http://localhost:8000 && echo Press Ctrl+C to stop && echo. && php -S localhost:8000 -t ."
                Start-Sleep 3
                Start-Process "http://localhost:8000"
            }
            else {
                throw "PHP not found"
            }
        }
        catch {
            # Check for Python
            try {
                $pythonVersion = python --version 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "Python found! Starting Python HTTP server..." -ForegroundColor Green
                    Start-Process cmd -ArgumentList "/k", "echo BitBear System - Python Server && echo Server: http://localhost:8000 && echo Press Ctrl+C to stop && echo. && python -m http.server 8000"
                    Start-Sleep 3
                    Start-Process "http://localhost:8000"
                }
                else {
                    throw "Python not found"
                }
            }
            catch {
                Write-Host "No PHP or Python found. Opening static version..." -ForegroundColor Yellow
                if (Test-Path "index-standalone.html") {
                    Start-Process "index-standalone.html"
                }
                elseif (Test-Path "index.html") {
                    Start-Process "index.html"
                }
                else {
                    Write-Host "No suitable files found for startup." -ForegroundColor Red
                }
            }
        }
    }
    
    Write-Host ""
    Write-Host "================================" -ForegroundColor Green
    Write-Host "Setup Complete!" -ForegroundColor Green
    Write-Host "================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Version rollback and project startup completed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Access the project at:" -ForegroundColor Cyan
    Write-Host "- Server version: http://localhost:8000" -ForegroundColor White
    Write-Host "- Or check if browser opened automatically" -ForegroundColor White
    Write-Host ""
    Write-Host "Safety backup location: $safetyBackup" -ForegroundColor Cyan
    Write-Host ""
}
catch {
    Write-Host ""
    Write-Host "Error during rollback operation!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Attempting to restore from safety backup..." -ForegroundColor Yellow
    
    try {
        if (Test-Path $safetyBackup) {
            $safetyItems = Get-ChildItem $safetyBackup -Force
            foreach ($item in $safetyItems) {
                $destPath = Join-Path $currentDir $item.Name
                if ($item.PSIsContainer) {
                    Copy-Item $item.FullName $destPath -Recurse -Force
                } else {
                    Copy-Item $item.FullName $destPath -Force
                }
            }
            Write-Host "Safety restore completed." -ForegroundColor Green
        }
    }
    catch {
        Write-Host "Failed to restore from safety backup!" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Read-Host "Press Enter to exit"
