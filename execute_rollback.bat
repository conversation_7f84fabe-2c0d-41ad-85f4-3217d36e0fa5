@echo off
echo ================================
echo BitBear System Version Rollback
echo ================================
echo.

set "CURRENT_DIR=%~dp0"
set "BACKUP_PATH=D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1"

echo Backup path: %BACKUP_PATH%
echo Current directory: %CURRENT_DIR%
echo.

echo Verifying backup folder...
if not exist "%BACKUP_PATH%" (
    echo Error: Backup folder not found!
    pause
    exit /b 1
)

echo Backup folder verified successfully!
echo.

echo Listing backup contents:
dir "%BACKUP_PATH%" /b
echo.

echo WARNING: This will replace all current files with the backup version.
echo Current files will be backed up to a safety folder.
echo.
set /p "CONFIRM=Do you want to proceed? (Y/N): "

if /i not "%CONFIRM%"=="Y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Creating safety backup of current version...
set "TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "TIMESTAMP=%TIMESTAMP: =0%"
set "SAFETY_BACKUP=%CURRENT_DIR%current_backup_%TIMESTAMP%"

mkdir "%SAFETY_BACKUP%" 2>nul
echo Safety backup folder: %SAFETY_BACKUP%

echo Backing up current files...
for %%f in ("%CURRENT_DIR%*") do (
    if not "%%~nxf"=="%~nx0" (
        if not "%%~nxf"=="current_backup_%TIMESTAMP%" (
            if exist "%%f\" (
                xcopy "%%f" "%SAFETY_BACKUP%\%%~nxf\" /e /h /y >nul 2>&1
            ) else (
                copy "%%f" "%SAFETY_BACKUP%\" >nul 2>&1
            )
        )
    )
)

echo Current files backed up successfully!
echo.

echo Clearing current directory...
for /f "delims=" %%i in ('dir /b /a-d "%CURRENT_DIR%"') do (
    if not "%%i"=="%~nx0" (
        del /q "%CURRENT_DIR%%%i" >nul 2>&1
    )
)

for /f "delims=" %%i in ('dir /b /ad "%CURRENT_DIR%"') do (
    if not "%%~nxi"=="current_backup_%TIMESTAMP%" (
        rmdir /s /q "%CURRENT_DIR%%%i" >nul 2>&1
    )
)

echo Current directory cleared!
echo.

echo Restoring files from backup...
xcopy "%BACKUP_PATH%\*" "%CURRENT_DIR%" /e /h /y /i

if %errorlevel% neq 0 (
    echo.
    echo Error: Restore operation failed!
    echo Attempting to restore from safety backup...
    xcopy "%SAFETY_BACKUP%\*" "%CURRENT_DIR%" /e /h /y /i >nul 2>&1
    echo Safety restore completed.
    echo.
    pause
    exit /b 1
)

echo.
echo ================================
echo Rollback completed successfully!
echo ================================
echo.

echo Files restored from: %BACKUP_PATH%
echo Current version backed up to: %SAFETY_BACKUP%
echo.

echo Starting project...
echo.

REM Try to start the project using available methods
if exist "启动项目.bat" (
    echo Found startup script: 启动项目.bat
    echo Starting project with existing script...
    start "" "启动项目.bat"
    goto :startup_complete
)

if exist "start_server.bat" (
    echo Found startup script: start_server.bat
    start "" "start_server.bat"
    goto :startup_complete
)

if exist "快速启动后台.bat" (
    echo Found startup script: 快速启动后台.bat
    start "" "快速启动后台.bat"
    goto :startup_complete
)

echo No startup script found. Attempting manual startup...

REM Check for PHP
php --version >nul 2>&1
if %errorlevel% equ 0 (
    echo PHP found! Starting PHP development server...
    start "BitBear PHP Server" cmd /k "echo BitBear System - PHP Server && echo Server: http://localhost:8000 && echo Press Ctrl+C to stop && echo. && php -S localhost:8000 -t ."
    timeout /t 3 >nul
    goto :open_browser
)

REM Check for Python
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found! Starting Python HTTP server...
    start "BitBear Python Server" cmd /k "echo BitBear System - Python Server && echo Server: http://localhost:8000 && echo Press Ctrl+C to stop && echo. && python -m http.server 8000"
    timeout /t 3 >nul
    goto :open_browser
)

echo No PHP or Python found. Opening static version...
if exist "index-standalone.html" (
    start "" "index-standalone.html"
    goto :startup_complete
)

if exist "index.html" (
    start "" "index.html"
    goto :startup_complete
)

echo No suitable files found for startup.
goto :startup_complete

:open_browser
echo Opening browser...
timeout /t 2 >nul
start http://localhost:8000 >nul 2>&1

:startup_complete
echo.
echo ================================
echo Setup Complete!
echo ================================
echo.
echo Version rollback and project startup completed!
echo.
echo Access the project at:
echo - Server version: http://localhost:8000
echo - Or check if browser opened automatically
echo.
echo Safety backup location: %SAFETY_BACKUP%
echo.
pause
