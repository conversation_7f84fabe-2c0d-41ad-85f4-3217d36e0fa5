@echo off
title BitBear System Rollback
color 0A

echo.
echo ==========================================
echo   BitBear System Version Rollback
echo ==========================================
echo.

echo Step 1: Verify backup location
echo Checking: D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1
echo.

if exist "D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1" (
    echo [SUCCESS] Backup folder found!
    echo.
    echo Backup contents:
    dir "D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1" /b
    echo.
) else (
    echo [ERROR] Backup folder not found!
    echo Please check if the path is correct.
    echo.
    pause
    exit /b 1
)

echo Step 2: Create safety backup
set SAFETY_FOLDER=safety_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set SAFETY_FOLDER=%SAFETY_FOLDER: =0%

echo Creating safety backup folder: %SAFETY_FOLDER%
mkdir "%SAFETY_FOLDER%" 2>nul

echo Backing up current files...
for %%f in (*) do (
    if not "%%f"=="%~nx0" (
        if not "%%f"=="%SAFETY_FOLDER%" (
            copy "%%f" "%SAFETY_FOLDER%\" >nul 2>&1
        )
    )
)

for /d %%d in (*) do (
    if not "%%d"=="%SAFETY_FOLDER%" (
        xcopy "%%d" "%SAFETY_FOLDER%\%%d\" /e /i /h /y >nul 2>&1
    )
)

echo [SUCCESS] Current files backed up to: %SAFETY_FOLDER%
echo.

echo Step 3: Confirm rollback operation
echo WARNING: This will replace all current files with backup files.
echo Current files are safely backed up in: %SAFETY_FOLDER%
echo.
set /p CONFIRM=Do you want to continue? (Y/N): 

if /i not "%CONFIRM%"=="Y" (
    echo Operation cancelled by user.
    pause
    exit /b 0
)

echo.
echo Step 4: Clear current directory
echo Removing current files...

for %%f in (*) do (
    if not "%%f"=="%~nx0" (
        if not "%%f"=="%SAFETY_FOLDER%" (
            del "%%f" >nul 2>&1
        )
    )
)

for /d %%d in (*) do (
    if not "%%d"=="%SAFETY_FOLDER%" (
        rmdir /s /q "%%d" >nul 2>&1
    )
)

echo [SUCCESS] Current directory cleared.
echo.

echo Step 5: Copy files from backup
echo Copying files from backup...

xcopy "D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1\*" . /e /i /h /y

if %errorlevel% neq 0 (
    echo [ERROR] Failed to copy from backup!
    echo Restoring from safety backup...
    xcopy "%SAFETY_FOLDER%\*" . /e /i /h /y >nul 2>&1
    echo Files restored from safety backup.
    pause
    exit /b 1
)

echo [SUCCESS] Files copied from backup successfully!
echo.

echo Step 6: Start the project
echo Looking for startup scripts...

if exist "启动项目.bat" (
    echo Found: 启动项目.bat
    echo Starting project...
    start "" "启动项目.bat"
    goto :startup_done
)

if exist "start_server.bat" (
    echo Found: start_server.bat
    echo Starting project...
    start "" "start_server.bat"
    goto :startup_done
)

if exist "快速启动后台.bat" (
    echo Found: 快速启动后台.bat
    echo Starting project...
    start "" "快速启动后台.bat"
    goto :startup_done
)

if exist "index-standalone.html" (
    echo Found: index-standalone.html
    echo Opening static version...
    start "" "index-standalone.html"
    goto :startup_done
)

if exist "index.html" (
    echo Found: index.html
    echo Opening main page...
    start "" "index.html"
    goto :startup_done
)

echo No startup files found. Please start manually.

:startup_done
echo.
echo ==========================================
echo   Rollback Completed Successfully!
echo ==========================================
echo.
echo Summary:
echo - Files restored from backup
echo - Safety backup saved to: %SAFETY_FOLDER%
echo - Project startup initiated
echo.
echo If the project didn't start automatically:
echo 1. Look for startup scripts in the folder
echo 2. Or open index-standalone.html directly
echo 3. Or start a local server manually
echo.
pause
