比特熊智慧系统版本回退指南
================================

由于自动脚本执行遇到问题，请按照以下步骤手动进行版本回退：

📁 备份路径确认
--------------
备份文件夹：D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1
当前项目：d:\比特熊智慧系统(v0.0.1)

🔄 手动回退步骤
--------------

步骤1：创建当前版本的安全备份
1. 在当前项目文件夹中创建一个新文件夹，命名为"当前版本备份"
2. 将当前项目中的所有文件和文件夹复制到"当前版本备份"文件夹中
   （除了刚创建的备份文件夹本身）

步骤2：清空当前项目文件夹
1. 删除当前项目文件夹中的所有文件和文件夹
2. 保留"当前版本备份"文件夹

步骤3：从备份恢复文件
1. 打开备份文件夹：D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1
2. 选择备份文件夹中的所有文件和文件夹
3. 复制这些文件到当前项目文件夹：d:\比特熊智慧系统(v0.0.1)

步骤4：启动项目
1. 查找项目中的启动脚本：
   - 启动项目.bat
   - start_server.bat
   - 快速启动后台.bat
2. 双击运行找到的启动脚本
3. 如果没有启动脚本，双击打开 index-standalone.html

🚀 快速启动方法
--------------

方法1：使用启动脚本
- 双击"启动项目.bat"（如果存在）
- 按照提示选择启动模式

方法2：手动启动PHP服务器
- 打开命令提示符
- 切换到项目目录：cd /d "d:\比特熊智慧系统(v0.0.1)"
- 运行：php -S localhost:8000 -t .
- 在浏览器中访问：http://localhost:8000

方法3：手动启动Python服务器
- 打开命令提示符
- 切换到项目目录：cd /d "d:\比特熊智慧系统(v0.0.1)"
- 运行：python -m http.server 8000
- 在浏览器中访问：http://localhost:8000

方法4：直接打开静态版本
- 双击"index-standalone.html"文件

⚠️ 注意事项
-----------
1. 确保备份路径正确且文件夹存在
2. 在执行步骤2之前，务必完成步骤1的安全备份
3. 如果回退失败，可以从"当前版本备份"文件夹恢复原始文件
4. 建议在执行回退前关闭所有正在运行的服务器进程

🔧 故障排除
-----------
如果遇到问题：
1. 检查备份文件夹是否存在且可访问
2. 确保有足够的磁盘空间
3. 检查文件权限是否正确
4. 如果启动失败，尝试不同的启动方法

📞 需要帮助？
-----------
如果手动操作遇到困难，请：
1. 确认备份文件夹的确切路径
2. 检查当前项目文件夹的权限
3. 尝试使用管理员权限运行操作

完成回退后，项目应该可以正常访问：
- 服务器版本：http://localhost:8000
- 静态版本：直接打开HTML文件
