<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板布局预览</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 2rem;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .page-header {
            margin-bottom: 2rem;
        }
        
        .page-header h1 {
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        
        .page-header p {
            color: #64748b;
            margin: 0;
        }
        
        /* 统计卡片网格 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
        }
        
        /* 仪表板网格 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header h3 {
            margin: 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
        }
        
        .filter-tabs {
            display: flex;
            gap: 0.5rem;
        }
        
        .filter-tab {
            padding: 0.5rem 1rem;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
        }
        
        .filter-tab.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .card-content {
            padding: 1.5rem;
        }
        
        .sidebar-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            overflow: hidden;
        }
        
        .sidebar-card .card-header {
            padding: 1rem 1.5rem;
            background: #f8fafc;
        }
        
        .sidebar-card .card-header h4 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: #1e293b;
        }
        
        .btn {
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .recent-comments {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .recent-comment-item {
            padding: 1rem;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            gap: 0.75rem;
        }
        
        .recent-comment-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e2e8f0;
            flex-shrink: 0;
        }
        
        .recent-comment-content {
            flex: 1;
        }
        
        .recent-comment-user {
            font-weight: 600;
            color: #1e293b;
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }
        
        .recent-comment-text {
            color: #64748b;
            font-size: 0.875rem;
            line-height: 1.4;
            margin-bottom: 0.5rem;
        }
        
        .recent-comment-meta {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.75rem;
            color: #94a3b8;
        }
        
        .recent-comment-status {
            padding: 0.125rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .recent-comment-status.pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .recent-comment-status.approved {
            background: #d1fae5;
            color: #065f46;
        }
        
        .recent-comment-status.reported {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .stat-row:last-child {
            margin-bottom: 0;
        }
        
        .stat-row span:first-child {
            color: #64748b;
        }
        
        .stat-row span:last-child {
            font-weight: 600;
        }
        
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="page-header">
            <h1>评论管理 - 仪表板布局</h1>
            <p>管理和审核用户评论</p>
        </div>
        
        <!-- 统计卡片区域 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">
                    💬
                </div>
                <div class="stat-content">
                    <div class="stat-number">6</div>
                    <div class="stat-label">总评论数</div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                    ⏰
                </div>
                <div class="stat-content">
                    <div class="stat-number">3</div>
                    <div class="stat-label">待审核</div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    ✅
                </div>
                <div class="stat-content">
                    <div class="stat-number">3</div>
                    <div class="stat-label">已通过</div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                    ⚠️
                </div>
                <div class="stat-content">
                    <div class="stat-number">2</div>
                    <div class="stat-label">被举报</div>
                </div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="dashboard-grid">
            <!-- 最近评论卡片 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>最近评论</h3>
                    <div class="filter-tabs">
                        <button class="filter-tab active">全部</button>
                        <button class="filter-tab">待审核</button>
                        <button class="filter-tab">被举报</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="recent-comments">
                        <div class="recent-comment-item">
                            <div class="recent-comment-avatar"></div>
                            <div class="recent-comment-content">
                                <div class="recent-comment-user">超级管理员</div>
                                <div class="recent-comment-text">这是一条已通过的评论，用于演示评论管理功能。内容正常，没有违规。</div>
                                <div class="recent-comment-meta">
                                    <span>08-02 10:30</span>
                                    <span class="recent-comment-status approved">已通过</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="recent-comment-item">
                            <div class="recent-comment-avatar"></div>
                            <div class="recent-comment-content">
                                <div class="recent-comment-user">新手小白</div>
                                <div class="recent-comment-text">这是一条待审核的评论，需要管理员审核后才能显示。内容可能包含敏感词汇。</div>
                                <div class="recent-comment-meta">
                                    <span>08-02 09:15</span>
                                    <span class="recent-comment-status pending">待审核</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="recent-comment-item">
                            <div class="recent-comment-avatar"></div>
                            <div class="recent-comment-content">
                                <div class="recent-comment-user">争议用户</div>
                                <div class="recent-comment-text">这条评论被多人举报，可能包含不当内容，需要管理员特别关注和处理。</div>
                                <div class="recent-comment-meta">
                                    <span>08-02 08:45</span>
                                    <span class="recent-comment-status approved">已通过</span>
                                    <span class="recent-comment-status reported">被举报</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="dashboard-sidebar">
                <!-- 快速操作 -->
                <div class="sidebar-card">
                    <div class="card-header">
                        <h4>快速操作</h4>
                    </div>
                    <div class="card-content">
                        <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                            <button class="btn btn-primary" style="width: 100%;">
                                📝 查看所有评论
                            </button>
                            <button class="btn btn-warning" style="width: 100%;">
                                ⏰ 审核待处理
                            </button>
                            <button class="btn btn-danger" style="width: 100%;">
                                ⚠️ 处理举报
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 评论统计 -->
                <div class="sidebar-card">
                    <div class="card-header">
                        <h4>本月统计</h4>
                    </div>
                    <div class="card-content">
                        <div class="stat-row">
                            <span>新增评论</span>
                            <span style="color: #1e293b;">6</span>
                        </div>
                        <div class="stat-row">
                            <span>通过率</span>
                            <span style="color: #10b981;">50%</span>
                        </div>
                        <div class="stat-row">
                            <span>举报处理</span>
                            <span style="color: #ef4444;">2</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
