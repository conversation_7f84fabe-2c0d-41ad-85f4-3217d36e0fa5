@echo off
chcp 65001 >nul
echo ================================
echo 比特熊智慧系统智能版本回退脚本
echo ================================
echo.

set "CURRENT_DIR=%~dp0"
set "TEMP_BACKUP=%CURRENT_DIR%temp_current_backup"

echo 正在搜索备份文件夹...
echo.

REM 搜索可能的备份路径
set "BACKUP_PATH="
for %%d in (C D E F G) do (
    if exist "%%d:\比特熊组织智慧系统备份文件" (
        for /f "delims=" %%f in ('dir /b /ad "%%d:\比特熊组织智慧系统备份文件\*第6次备份*" 2^>nul') do (
            set "BACKUP_PATH=%%d:\比特熊组织智慧系统备份文件\%%f"
            goto :found
        )
    )
)

REM 如果没找到第6次备份，查找其他备份
if not defined BACKUP_PATH (
    echo 未找到第6次备份，正在搜索其他备份...
    for %%d in (C D E F G) do (
        if exist "%%d:\比特熊组织智慧系统备份文件" (
            for /f "delims=" %%f in ('dir /b /ad "%%d:\比特熊组织智慧系统备份文件\*备份*" 2^>nul') do (
                set "BACKUP_PATH=%%d:\比特熊组织智慧系统备份文件\%%f"
                echo 找到备份：%%f
                goto :found
            )
        )
    )
)

if not defined BACKUP_PATH (
    echo 错误：未找到任何备份文件夹！
    echo.
    echo 请确认以下事项：
    echo 1. 备份文件夹是否存在
    echo 2. 备份文件夹路径是否正确
    echo 3. 备份文件夹是否在C、D、E、F、G盘中
    echo.
    echo 手动指定备份路径：
    set /p "BACKUP_PATH=请输入完整的备份文件夹路径: "
    if not exist "%BACKUP_PATH%" (
        echo 指定的路径不存在！
        pause
        exit /b 1
    )
)

:found
echo 找到备份文件夹：%BACKUP_PATH%
echo.

echo 正在验证备份文件夹内容...
if not exist "%BACKUP_PATH%\index.php" (
    if not exist "%BACKUP_PATH%\index.html" (
        echo 警告：备份文件夹中未找到主要文件，是否继续？
        set /p "confirm=继续？(y/n): "
        if /i not "%confirm%"=="y" exit /b 1
    )
)

echo 正在创建当前版本的安全备份...
if exist "%TEMP_BACKUP%" rmdir /s /q "%TEMP_BACKUP%"
mkdir "%TEMP_BACKUP%"

REM 备份当前版本（排除临时文件和脚本）
for %%f in ("%CURRENT_DIR%*") do (
    if not "%%~nxf"=="%~nx0" (
        if not "%%~nxf"=="版本回退脚本.bat" (
            if not "%%~nxf"=="智能版本回退.bat" (
                xcopy "%%f" "%TEMP_BACKUP%\" /e /h /y >nul 2>&1
            )
        )
    )
)

echo 正在清理当前目录...
for /f "delims=" %%i in ('dir /b /a-d "%CURRENT_DIR%"') do (
    if not "%%i"=="%~nx0" (
        if not "%%i"=="版本回退脚本.bat" (
            if not "%%i"=="智能版本回退.bat" (
                del /q "%CURRENT_DIR%%%i" >nul 2>&1
            )
        )
    )
)

for /f "delims=" %%i in ('dir /b /ad "%CURRENT_DIR%"') do (
    if not "%%i"=="temp_current_backup" (
        rmdir /s /q "%CURRENT_DIR%%%i" >nul 2>&1
    )
)

echo 正在从备份恢复文件...
xcopy "%BACKUP_PATH%\*" "%CURRENT_DIR%" /e /h /y >nul 2>&1

if %errorlevel% neq 0 (
    echo 错误：恢复失败！正在回滚到原始状态...
    xcopy "%TEMP_BACKUP%\*" "%CURRENT_DIR%" /e /h /y >nul 2>&1
    rmdir /s /q "%TEMP_BACKUP%"
    echo 已回滚到原始状态
    pause
    exit /b 1
)

echo 版本回退成功！
echo.

echo 正在清理临时文件...
rmdir /s /q "%TEMP_BACKUP%"

echo 正在自动启动项目...
echo.

REM 尝试多种启动方式
if exist "启动项目.bat" (
    echo 使用启动项目.bat启动...
    start "" "启动项目.bat"
    timeout /t 3 >nul
) else if exist "start_server.bat" (
    echo 使用start_server.bat启动...
    start "" "start_server.bat"
    timeout /t 3 >nul
) else if exist "快速启动后台.bat" (
    echo 使用快速启动后台.bat启动...
    start "" "快速启动后台.bat"
    timeout /t 3 >nul
) else (
    echo 未找到专用启动脚本，尝试自动启动...
    
    REM 检查并启动PHP服务器
    php --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo 启动PHP开发服务器...
        start "比特熊智慧系统-PHP服务器" cmd /k "php -S localhost:8000 -t . && pause"
        echo PHP服务器启动中... 请稍候
        timeout /t 2 >nul
    ) else (
        echo PHP不可用，尝试Python服务器...
        python --version >nul 2>&1
        if %errorlevel% equ 0 (
            start "比特熊智慧系统-Python服务器" cmd /k "python -m http.server 8000 && pause"
            echo Python服务器启动中... 请稍候
            timeout /t 2 >nul
        ) else (
            echo 未找到PHP或Python环境
            echo 请手动启动项目或安装PHP/Python环境
        )
    )
)

REM 尝试打开浏览器
timeout /t 3 >nul
start http://localhost:8000 >nul 2>&1

echo.
echo ================================
echo 版本回退完成！
echo ================================
echo.
echo 项目应该已经启动，请检查：
echo 1. 浏览器是否自动打开 http://localhost:8000
echo 2. 如果没有自动打开，请手动访问该地址
echo 3. 如果服务器未启动，请运行相应的启动脚本
echo.
echo 当前备份保存在：%TEMP_BACKUP%（已清理）
echo 原始备份位置：%BACKUP_PATH%
echo.
pause
