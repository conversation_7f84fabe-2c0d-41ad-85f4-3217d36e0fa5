<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比特熊智慧系统 - 版本回退指南</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 30px;
        }
        .step {
            background: #f8f9fa;
            border-left: 5px solid #4CAF50;
            margin: 20px 0;
            padding: 20px;
            border-radius: 0 10px 10px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step h3 {
            color: #4CAF50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .path {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            border: 1px solid #2196F3;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .button {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px 5px;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        .button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        .folder-icon {
            color: #FFA726;
            font-size: 1.2em;
        }
        .check-icon {
            color: #4CAF50;
            font-size: 1.2em;
        }
        ol {
            padding-left: 0;
        }
        ol li {
            margin: 10px 0;
            padding: 10px;
            background: #f1f3f4;
            border-radius: 5px;
            list-style-position: inside;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 版本回退指南</h1>
            <p>比特熊智慧系统版本回退操作指南</p>
        </div>
        
        <div class="content">
            <div class="warning">
                <strong>⚠️ 重要提醒：</strong>
                在执行回退操作前，请确保已经创建了当前版本的备份，以防回退失败时可以恢复。
            </div>

            <div class="step">
                <h3><span class="folder-icon">📁</span> 步骤1：确认文件夹路径</h3>
                <p><strong>备份文件夹路径：</strong></p>
                <div class="path">D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1</div>
                <p><strong>当前项目路径：</strong></p>
                <div class="path">d:\比特熊智慧系统(v0.0.1)</div>
                <p>请确认这两个文件夹都存在且可以访问。</p>
            </div>

            <div class="step">
                <h3><span class="check-icon">💾</span> 步骤2：创建安全备份</h3>
                <ol>
                    <li>在当前项目文件夹中创建一个新文件夹，命名为 <strong>"当前版本安全备份"</strong></li>
                    <li>选择当前项目文件夹中的所有文件和文件夹（除了刚创建的备份文件夹）</li>
                    <li>复制这些文件到 "当前版本安全备份" 文件夹中</li>
                    <li>确认备份完成后再继续下一步</li>
                </ol>
            </div>

            <div class="step">
                <h3><span class="check-icon">🗑️</span> 步骤3：清空当前项目文件夹</h3>
                <ol>
                    <li>选择当前项目文件夹中的所有文件和文件夹</li>
                    <li><strong>保留</strong> "当前版本安全备份" 文件夹</li>
                    <li>删除其他所有文件和文件夹</li>
                </ol>
                <div class="warning">
                    <strong>注意：</strong>请确保已经完成步骤2的安全备份，再执行此步骤！
                </div>
            </div>

            <div class="step">
                <h3><span class="check-icon">📋</span> 步骤4：从备份恢复文件</h3>
                <ol>
                    <li>打开备份文件夹：
                        <div class="path">D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1</div>
                    </li>
                    <li>选择备份文件夹中的所有文件和文件夹</li>
                    <li>复制这些文件</li>
                    <li>粘贴到当前项目文件夹：
                        <div class="path">d:\比特熊智慧系统(v0.0.1)</div>
                    </li>
                    <li>等待复制完成</li>
                </ol>
            </div>

            <div class="step">
                <h3><span class="check-icon">🚀</span> 步骤5：启动项目</h3>
                <p>回退完成后，尝试以下方法启动项目：</p>
                <ol>
                    <li><strong>方法1：</strong>双击 <code>启动项目.bat</code> 文件（如果存在）</li>
                    <li><strong>方法2：</strong>双击 <code>index-standalone.html</code> 文件</li>
                    <li><strong>方法3：</strong>双击 <code>index.html</code> 文件</li>
                    <li><strong>方法4：</strong>手动启动服务器（见下方说明）</li>
                </ol>
            </div>

            <div class="step">
                <h3><span class="check-icon">⚙️</span> 手动启动服务器（可选）</h3>
                <p>如果需要完整功能，可以手动启动服务器：</p>
                <ol>
                    <li>按 <kbd>Win + R</kbd> 打开运行对话框</li>
                    <li>输入 <code>cmd</code> 并按回车</li>
                    <li>在命令提示符中输入：<br>
                        <div class="path">cd /d "d:\比特熊智慧系统(v0.0.1)"</div>
                    </li>
                    <li>然后输入以下命令之一：
                        <div class="path">php -S localhost:8000 -t .</div>
                        或
                        <div class="path">python -m http.server 8000</div>
                    </li>
                    <li>在浏览器中访问：<code>http://localhost:8000</code></li>
                </ol>
            </div>

            <div class="success">
                <h3><span class="check-icon">✅</span> 完成！</h3>
                <p>如果一切顺利，您的项目现在应该已经回退到第6次备份的版本并正在运行。</p>
                <p>如果遇到问题，可以从 "当前版本安全备份" 文件夹恢复原始文件。</p>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="#" class="button" onclick="window.close()">关闭指南</a>
                <a href="index-standalone.html" class="button">打开静态版本</a>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                step.style.opacity = '0';
                step.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    step.style.transition = 'all 0.5s ease';
                    step.style.opacity = '1';
                    step.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
