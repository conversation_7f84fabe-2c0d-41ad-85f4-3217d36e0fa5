<?php
// 主页数据获取函数
function getHomepageData() {
    try {
        require_once 'config/database.php';
        $db = db();
        
        // 获取英雄区域内容
        $hero = $db->fetchOne("SELECT * FROM homepage_hero WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
        
        // 获取专家列表
        $experts = $db->fetchAll("SELECT * FROM homepage_experts WHERE is_active = 1 ORDER BY sort_order ASC, id ASC");
        
        // 获取视频内容
        $video = $db->fetchOne("SELECT * FROM homepage_videos WHERE is_active = 1 AND is_featured = 1 ORDER BY id DESC LIMIT 1");
        
        // 获取区域设置
        $sections = $db->fetchAll("SELECT * FROM homepage_sections WHERE is_visible = 1 ORDER BY sort_order ASC, id ASC");
        
        return [
            'hero' => $hero,
            'experts' => $experts ?: [],
            'video' => $video,
            'sections' => $sections ?: []
        ];
        
    } catch (Exception $e) {
        error_log("获取主页数据失败: " . $e->getMessage());
        
        // 返回默认数据
        return getDefaultHomepageData();
    }
}

function getDefaultHomepageData() {
    return [
        'hero' => [
            'title' => '欢迎访问比特熊极简门户网站',
            'subtitle' => '在这里一起和小熊彼彼度过美好的时光',
            'description' => '也许你会好奇，这个网站是干嘛的？比特熊极简门户网站是比特熊爱生活团队开发的一个综合应用型社交网站，你可以在这里学习、加入在线联机游戏、分享生活中的美好事物、或者一起学习，读书等',
            'primary_button_text' => '立即体验',
            'primary_button_url' => '#',
            'secondary_button_text' => '了解更多',
            'secondary_button_url' => '#features',
            'hero_image' => 'image/bitlogo.png'
        ],
        'experts' => [
            [
                'id' => 1,
                'name' => 'Arianne Dee',
                'title' => 'Software developer',
                'description' => '资深软件开发工程师，专注于Python和Web开发',
                'image' => 'image/default-avatar.svg'
            ],
            [
                'id' => 2,
                'name' => 'Sari Greene',
                'title' => 'Cybersecurity practitioner',
                'description' => '网络安全专家，在信息安全领域有丰富经验',
                'image' => 'image/default-avatar.svg'
            ],
            [
                'id' => 3,
                'name' => 'Bruno Gonçalves',
                'title' => 'Senior data scientist',
                'description' => '高级数据科学家，专注于机器学习和数据分析',
                'image' => 'image/default-avatar.svg'
            ],
            [
                'id' => 4,
                'name' => 'Neal Ford',
                'title' => 'Software architect',
                'description' => '软件架构师，在企业级应用设计方面经验丰富',
                'image' => 'image/default-avatar.svg'
            ],
            [
                'id' => 5,
                'name' => 'Kelsey Hightower',
                'title' => 'Software engineer',
                'description' => '软件工程师，云原生技术专家',
                'image' => 'image/default-avatar.svg'
            ],
            [
                'id' => 6,
                'name' => 'Ken Kousen',
                'title' => 'Java Champion',
                'description' => 'Java冠军，在Java生态系统方面有深厚造诣',
                'image' => 'image/default-avatar.svg'
            ]
        ],
        'video' => [
            'title' => 'Why Jose uses O\'Reilly every day',
            'description' => 'As a principal software engineer, I rely on O\'Reilly\'s platform to keep my team updated with the latest technologies and best practices.',
            'speaker_name' => 'Jose Dunio',
            'speaker_role' => 'Principal Software Engineer',
            'company_badge' => 'O\'REILLY',
            'video_url' => '#'
        ],
        'sections' => [
            ['section_name' => 'hero', 'is_visible' => 1],
            ['section_name' => 'stats', 'is_visible' => 1],
            ['section_name' => 'oreilly-hero', 'is_visible' => 1],
            ['section_name' => 'experts', 'is_visible' => 1],
            ['section_name' => 'testimonial', 'is_visible' => 1]
        ]
    ];
}

// 检查区域是否可见
function isSectionVisible($sections, $sectionName) {
    foreach ($sections as $section) {
        if ($section['section_name'] === $sectionName) {
            return $section['is_visible'] == 1;
        }
    }
    return true; // 默认显示
}

// 转义HTML输出
function escapeHtml($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

// 安全输出URL
function escapeUrl($url) {
    return htmlspecialchars($url, ENT_QUOTES, 'UTF-8');
}
?>
