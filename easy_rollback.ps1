# Simple BitBear System Rollback
Write-Host "BitBear System Easy Rollback" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green

$backupPath = "D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1"
$currentPath = Get-Location

Write-Host "Backup Path: $backupPath"
Write-Host "Current Path: $currentPath"
Write-Host ""

# Step 1: Check backup exists
Write-Host "Step 1: Checking backup folder..." -ForegroundColor Yellow
if (Test-Path $backupPath) {
    Write-Host "✓ Backup folder found!" -ForegroundColor Green
} else {
    Write-Host "✗ Backup folder not found!" -ForegroundColor Red
    Write-Host "Please check the path: $backupPath"
    Read-Host "Press Enter to exit"
    exit
}

# Step 2: Show backup contents
Write-Host ""
Write-Host "Step 2: Backup folder contents:" -ForegroundColor Yellow
try {
    $backupItems = Get-ChildItem $backupPath -ErrorAction Stop
    $backupItems | ForEach-Object { Write-Host "  - $($_.Name)" }
    Write-Host "Total items: $($backupItems.Count)"
} catch {
    Write-Host "Error reading backup folder: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

# Step 3: Confirm operation
Write-Host ""
Write-Host "Step 3: Confirmation" -ForegroundColor Yellow
Write-Host "This will replace current files with backup files."
$confirm = Read-Host "Continue? (y/n)"
if ($confirm -ne 'y' -and $confirm -ne 'Y') {
    Write-Host "Operation cancelled."
    Read-Host "Press Enter to exit"
    exit
}

# Step 4: Create safety backup
Write-Host ""
Write-Host "Step 4: Creating safety backup..." -ForegroundColor Yellow
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$safetyPath = Join-Path $currentPath "safety_backup_$timestamp"

try {
    New-Item -ItemType Directory -Path $safetyPath -Force | Out-Null
    
    $currentItems = Get-ChildItem $currentPath | Where-Object { 
        $_.Name -ne "easy_rollback.ps1" -and 
        $_.Name -notlike "safety_backup_*" 
    }
    
    foreach ($item in $currentItems) {
        $destPath = Join-Path $safetyPath $item.Name
        Copy-Item $item.FullName $destPath -Recurse -Force
    }
    
    Write-Host "✓ Safety backup created: $safetyPath" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to create safety backup: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

# Step 5: Clear current directory
Write-Host ""
Write-Host "Step 5: Clearing current directory..." -ForegroundColor Yellow
try {
    foreach ($item in $currentItems) {
        Remove-Item $item.FullName -Recurse -Force
    }
    Write-Host "✓ Current directory cleared" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to clear directory: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Attempting to restore from safety backup..."
    
    try {
        $safetyItems = Get-ChildItem $safetyPath
        foreach ($item in $safetyItems) {
            $destPath = Join-Path $currentPath $item.Name
            Copy-Item $item.FullName $destPath -Recurse -Force
        }
        Write-Host "✓ Restored from safety backup" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to restore from safety backup!" -ForegroundColor Red
    }
    
    Read-Host "Press Enter to exit"
    exit
}

# Step 6: Copy from backup
Write-Host ""
Write-Host "Step 6: Copying files from backup..." -ForegroundColor Yellow
try {
    foreach ($item in $backupItems) {
        $destPath = Join-Path $currentPath $item.Name
        Copy-Item $item.FullName $destPath -Recurse -Force
    }
    Write-Host "✓ Files copied from backup successfully!" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to copy from backup: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Attempting to restore from safety backup..."
    
    try {
        $safetyItems = Get-ChildItem $safetyPath
        foreach ($item in $safetyItems) {
            $destPath = Join-Path $currentPath $item.Name
            Copy-Item $item.FullName $destPath -Recurse -Force
        }
        Write-Host "✓ Restored from safety backup" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to restore from safety backup!" -ForegroundColor Red
    }
    
    Read-Host "Press Enter to exit"
    exit
}

# Step 7: Start project
Write-Host ""
Write-Host "Step 7: Starting project..." -ForegroundColor Yellow

if (Test-Path "启动项目.bat") {
    Write-Host "Found startup script: 启动项目.bat" -ForegroundColor Green
    Start-Process "启动项目.bat"
    Write-Host "✓ Project started with startup script" -ForegroundColor Green
} elseif (Test-Path "index-standalone.html") {
    Write-Host "Found static version: index-standalone.html" -ForegroundColor Green
    Start-Process "index-standalone.html"
    Write-Host "✓ Static version opened" -ForegroundColor Green
} elseif (Test-Path "index.html") {
    Write-Host "Found index.html" -ForegroundColor Green
    Start-Process "index.html"
    Write-Host "✓ Index page opened" -ForegroundColor Green
} else {
    Write-Host "No startup file found. Please start manually." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "================================" -ForegroundColor Green
Write-Host "Rollback Completed Successfully!" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""
Write-Host "Summary:" -ForegroundColor Cyan
Write-Host "- Files restored from: $backupPath"
Write-Host "- Safety backup saved to: $safetyPath"
Write-Host "- Project should be starting now"
Write-Host ""
Write-Host "If you need to access the project:"
Write-Host "- Check if browser opened automatically"
Write-Host "- Or visit: http://localhost:8000"
Write-Host "- Or double-click startup scripts in the folder"

Read-Host "Press Enter to exit"
