<?php
/**
 * 通知管理API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// 获取请求方法和参数
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

// 模拟用户ID（实际应用中应该从session获取）
$userId = 1; // 假设当前用户ID为1

try {
    $db = db();
    
    switch ($method) {
        case 'GET':
            handleGet($db, $userId);
            break;
        case 'PUT':
            handlePut($db, $userId, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取通知
 */
function handleGet($db, $userId) {
    $action = $_GET['action'] ?? 'list';
    
    if ($action === 'count') {
        // 获取未读通知数量
        $sql = "SELECT COUNT(*) as count FROM notifications WHERE (user_id = ? OR user_id IS NULL) AND is_read = 0";
        $result = $db->fetchOne($sql, [$userId]);
        echo json_encode(['success' => true, 'data' => ['count' => $result['count']]]);
    } else {
        // 获取通知列表
        $limit = $_GET['limit'] ?? 10;
        $sql = "SELECT id, title, content, type, is_read, created_at,
                       CASE 
                           WHEN TIMESTAMPDIFF(MINUTE, created_at, NOW()) < 60 THEN CONCAT(TIMESTAMPDIFF(MINUTE, created_at, NOW()), '分钟前')
                           WHEN TIMESTAMPDIFF(HOUR, created_at, NOW()) < 24 THEN CONCAT(TIMESTAMPDIFF(HOUR, created_at, NOW()), '小时前')
                           ELSE CONCAT(TIMESTAMPDIFF(DAY, created_at, NOW()), '天前')
                       END as time_ago
                FROM notifications 
                WHERE (user_id = ? OR user_id IS NULL) 
                ORDER BY created_at DESC 
                LIMIT ?";
        
        $notifications = $db->fetchAll($sql, [$userId, (int)$limit]);
        echo json_encode(['success' => true, 'data' => $notifications]);
    }
}

/**
 * 处理PUT请求 - 更新通知状态
 */
function handlePut($db, $userId, $input) {
    $action = $input['action'] ?? '';
    
    if ($action === 'mark_all_read') {
        // 标记所有通知为已读
        $sql = "UPDATE notifications SET is_read = 1 WHERE (user_id = ? OR user_id IS NULL) AND is_read = 0";
        $db->query($sql, [$userId]);
        
        echo json_encode(['success' => true, 'message' => '所有通知已标记为已读']);
    } elseif ($action === 'mark_read' && isset($input['id'])) {
        // 标记单个通知为已读
        $sql = "UPDATE notifications SET is_read = 1 WHERE id = ? AND (user_id = ? OR user_id IS NULL)";
        $db->query($sql, [$input['id'], $userId]);
        
        echo json_encode(['success' => true, 'message' => '通知已标记为已读']);
    } else {
        http_response_code(400);
        echo json_encode(['error' => '无效的操作']);
    }
}

/**
 * 创建新通知（系统内部使用）
 */
function createNotification($db, $title, $content, $type = 'system_update', $userId = null) {
    $sql = "INSERT INTO notifications (user_id, title, content, type) VALUES (?, ?, ?, ?)";
    $db->query($sql, [$userId, $title, $content, $type]);
    return $db->lastInsertId();
}
?>
