<?php
/**
 * 时间处理辅助函数
 */

// 确保时区设置正确
if (!ini_get('date.timezone')) {
    date_default_timezone_set('Asia/Shanghai');
}

/**
 * 格式化时间为"多久前"的形式
 * @param string $datetime 时间字符串
 * @return string 格式化后的时间字符串
 */
function timeAgo($datetime) {
    if (empty($datetime)) {
        return '未知时间';
    }

    // 尝试多种时间格式解析
    $timestamp = false;

    // 首先尝试直接解析
    $timestamp = strtotime($datetime);

    // 如果失败，尝试其他格式
    if ($timestamp === false) {
        // 尝试ISO格式
        $timestamp = strtotime(str_replace('T', ' ', $datetime));
    }

    // 如果还是失败，尝试手动解析
    if ($timestamp === false) {
        // 匹配 YYYY-MM-DD HH:MM:SS 格式
        if (preg_match('/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/', $datetime, $matches)) {
            $timestamp = mktime($matches[4], $matches[5], $matches[6], $matches[2], $matches[3], $matches[1]);
        }
    }

    if ($timestamp === false) {
        return '时间格式错误';
    }

    $currentTime = time();
    $time = $currentTime - $timestamp;

    // 如果时间差为负数（未来时间），显示具体时间
    if ($time < 0) {
        return date('Y-m-d H:i', $timestamp);
    }

    // 3分钟内显示"刚刚"
    if ($time < 180) return '刚刚';

    // 1小时内显示分钟
    if ($time < 3600) return floor($time/60) . '分钟前';

    // 24小时内显示小时
    if ($time < 86400) return floor($time/3600) . '小时前';

    // 30天内显示天数
    if ($time < 2592000) return floor($time/86400) . '天前';

    // 12个月内显示月数
    if ($time < 31536000) return floor($time/2592000) . '个月前';

    // 超过12个月显示年数
    return floor($time/31536000) . '年前';
}

/**
 * 获取当前时间字符串（兼容MySQL和SQLite）
 * @return string 当前时间字符串
 */
function getCurrentTime() {
    return date('Y-m-d H:i:s');
}

/**
 * 格式化数字显示
 * @param int $num 数字
 * @return string 格式化后的数字
 */
function formatNumber($num) {
    if ($num >= 1000000) return round($num/1000000, 1) . 'M';
    if ($num >= 1000) return round($num/1000, 1) . 'K';
    return $num;
}

/**
 * 转义HTML字符
 * @param string $text 文本
 * @return string 转义后的文本
 */
function escapeHtml($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

/**
 * 检查时间字符串是否有效
 * @param string $datetime 时间字符串
 * @return bool 是否有效
 */
function isValidDateTime($datetime) {
    if (empty($datetime)) {
        return false;
    }
    
    $timestamp = strtotime($datetime);
    return $timestamp !== false;
}

/**
 * 将时间转换为标准格式
 * @param string $datetime 时间字符串
 * @return string 标准格式的时间字符串
 */
function normalizeDateTime($datetime) {
    if (empty($datetime)) {
        return getCurrentTime();
    }
    
    $timestamp = strtotime($datetime);
    if ($timestamp === false) {
        return getCurrentTime();
    }
    
    return date('Y-m-d H:i:s', $timestamp);
}
?>
