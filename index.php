<?php
// 设置session配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // 本地开发环境设为0
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.use_strict_mode', 1);

session_start();

// 引入数据库配置和主页数据函数
$db_available = false;
try {
    require_once 'config/database.php';
    require_once 'includes/homepage-data.php';
    require_once 'includes/oreilly-data.php';
    require_once 'classes/Auth.php';
    $db = db();
    $db_available = true;

    // 检查用户登录状态
    $auth = new Auth();
    $currentUser = $auth->getCurrentUser();



    // 获取主页数据
    $homepageData = getHomepageData();
    $hero = $homepageData['hero'];
    $experts = $homepageData['experts'];
    $video = $homepageData['video'];
    $sections = $homepageData['sections'];

    // 获取O'Reilly区域数据
    $oreillyData = getOreillyData();
    $oreillyHero = $oreillyData['hero'];
    $oreillyCourses = $oreillyData['courses'];
    $oreillyExperts = $oreillyData['experts'];
    $oreillyTestimonial = $oreillyData['testimonial'];
    $oreillyCta = $oreillyData['cta'];

} catch (Exception $e) {
    error_log("数据库连接失败: " . $e->getMessage());
    // 数据库不可用时使用默认数据
    $homepageData = getDefaultHomepageData();
    $hero = $homepageData['hero'];
    $experts = $homepageData['experts'];
    $video = $homepageData['video'];
    $sections = $homepageData['sections'];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<!-- 设置页面语言为中文 -->
<head>
    <meta charset="UTF-8">
    <!-- 设置字符编码为UTF-8 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 设置视口元标签，确保在移动设备上正确显示 -->
    <meta name="description" content="比特熊极简门户网站 - 综合应用型社交网站，学习、游戏、分享生活美好事物">
    <!-- 设置页面描述，有利于SEO优化 -->
    <meta name="keywords" content="比特熊,社交网站,在线游戏,学习,分享">
    <!-- 设置关键词，有利于SEO优化 -->
    <title>比特熊极简门户网站 - 一起度过美好时光</title>
    <!-- 设置页面标题 -->
    <link rel="stylesheet" href="index-style.css?v=20250731v1">
    <!-- 引入主要样式表 -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- 引入AOS动画库样式 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <!-- 预连接到Google字体服务器，提高加载速度 -->
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- 预连接到Google字体静态资源服务器 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- 引入现代化的Inter字体 -->
    <link rel="icon" type="image/png" href="image/bit.png">
    <!-- 设置网站图标 -->
</head>
<body>
    <header class="header">
    <!-- 网站的第一块区域 头部部分-->
        <div class="center">
        <!-- 网站区域：居中盒子 让内容居中显示-->
            <img src="image/bit.png" width="130px" height="60px">
            <!-- 插入比特熊logo图片-->
            <ul class="list-left" id="dynamicNavbar">
            <!-- 动态导航栏将在这里加载 -->
            <?php
            // 包含导航栏组件并获取数据
            require_once 'components/navbar.php';
            $navbarData = getNavbarData();

            // 渲染导航菜单项
            foreach ($navbarData as $item) {
                $hasChildren = !empty($item['children']);
                $itemClass = $hasChildren ? 'nav-item dropdown' : 'nav-item';

                if ($hasChildren) {
                    echo '<li class="' . $itemClass . '">';
                    echo '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link dropdown-toggle">';
                    echo htmlspecialchars($item['name']);
                    echo '<svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">';
                    echo '<path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/>';
                    echo '</svg>';
                    echo '</a>';
                    echo '<ul class="dropdown-menu">';
                    foreach ($item['children'] as $child) {
                        echo '<li><a href="' . htmlspecialchars($child['url']) . '" class="dropdown-item">' . htmlspecialchars($child['name']) . '</a></li>';
                    }
                    echo '</ul>';
                    echo '</li>';
                } else {
                    echo '<li class="' . $itemClass . '">';
                    echo '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link">' . htmlspecialchars($item['name']) . '</a>';
                    echo '</li>';
                }
            }
            ?>
            </ul>
            <!-- 搜索功能区域 -->
            <div class="search-container">
                <!-- 搜索表单 -->
                <form class="search-form" role="search">
                    <!-- 搜索输入框 -->
                    <div class="search-input-wrapper">
                        <input
                            type="search"
                            class="search-input"
                            placeholder="搜索课程、文章、资源..."
                            aria-label="搜索内容"
                            autocomplete="off"
                        >
                        <!-- 搜索按钮 -->
                        <button type="submit" class="search-btn" aria-label="搜索">
                            <!-- 搜索图标 -->
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    <!-- 搜索建议下拉框 -->
                    <div class="search-suggestions" id="searchSuggestions">
                        <!-- 热门搜索 -->
                        <div class="suggestions-section">
                            <h4 class="suggestions-title">热门搜索</h4>
                            <div class="suggestion-tags">
                                <span class="suggestion-tag">在线课程</span>
                                <span class="suggestion-tag">课程超市</span>
                                <span class="suggestion-tag">程序设计</span>
                                <span class="suggestion-tag">联机玩游戏</span>
                            </div>
                        </div>
                        <!-- 最近搜索 -->
                        <div class="suggestions-section">
                            <h4 class="suggestions-title">最近搜索</h4>
                            <ul class="recent-searches">
                                <li><a href="#" class="recent-search-item">在线课程</a></li>
                                <li><a href="#" class="recent-search-item">课程超市</a></li>
                                <li><a href="#" class="recent-search-item">数据分析</a></li>
                            </ul>
                        </div>
                    </div>
                </form>
            </div>

            <ul class="list-right">
            <!-- 导航条部分 右边区域-->
                <!-- 移动端搜索按钮 -->
                <li class="mobile-search-btn-container">
                    <button class="mobile-search-btn" aria-label="搜索">
                        <!-- 搜索图标 -->
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </li>



                <?php if ($currentUser): ?>
                    <!-- 已登录用户显示 -->
                    <li class="user-info">
                        <div class="user-profile">
                            <img src="<?php echo htmlspecialchars($currentUser['avatar'] ?? 'assets/images/default-avatar.png'); ?>"
                                 alt="用户头像" class="user-avatar-img">
                            <span class="user-nickname"><?php echo htmlspecialchars($currentUser['nickname'] ?? $currentUser['username']); ?></span>
                            <div class="user-dropdown">
                                <a href="user-profile.php" class="dropdown-item">个人资料</a>
                                <a href="community.php" class="dropdown-item">社区</a>
                                <a href="bookmarks.php" class="dropdown-item">我的收藏</a>
                                <?php if ($auth->hasRole('admin') || $auth->hasRole('super_admin')): ?>
                                    <a href="admin" class="dropdown-item">管理后台</a>
                                <?php endif; ?>
                                <div class="dropdown-divider"></div>
                                <a href="#" class="dropdown-item logout-link" onclick="handleLogout(event)">退出登录</a>
                            </div>
                        </div>
                    </li>
                <?php else: ?>
                    <!-- 未登录用户显示 -->
                    <li class="zhuche">
                    <!--    注册区域-->
                        <a href="register.php">注册试试</a>
                    <!--  创建注册链接-->
                    </li>
                    <li class="loading">
                    <!-- 登录区域-->
                        <a href="login.php">登录</a>
                    <!--  创建登录链接-->
                    </li>
                    <li class="admin-link">
                    <!--    管理后台链接-->
                        <a href="admin" title="管理后台 (需要登录)">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            管理后台
                        </a>
                    <!--  创建管理后台链接-->
                    </li>
                <?php endif; ?>
            </ul>
            <!-- 导航条右部分结束-->

            <!-- 移动端菜单按钮 -->
            <button class="mobile-menu-btn" aria-label="打开菜单">
                <!-- 汉堡菜单图标 -->
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
            <!-- 移动端菜单按钮结束 -->
        </div>
        <!-- 居中盒子结束部分-->

        <!-- 移动端菜单遮罩层 -->
        <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>
    </header>
    <!--第一区域结束部分-->

    <!-- 移动端搜索模态框 -->
    <div class="mobile-search-modal" id="mobileSearchModal">
        <!-- 模态框背景遮罩 -->
        <div class="modal-backdrop"></div>
        <!-- 模态框内容 -->
        <div class="modal-content">
            <!-- 模态框头部 -->
            <div class="modal-header">
                <h3 class="modal-title">搜索</h3>
                <button class="modal-close-btn" aria-label="关闭搜索">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
            <!-- 模态框主体 -->
            <div class="modal-body">
                <!-- 移动端搜索表单 -->
                <form class="mobile-search-form" role="search">
                    <div class="mobile-search-input-wrapper">
                        <input
                            type="search"
                            class="mobile-search-input"
                            placeholder="搜索课程、文章、资源..."
                            aria-label="搜索内容"
                            autocomplete="off"
                        >
                        <button type="submit" class="mobile-search-submit-btn" aria-label="搜索">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>

                    <!-- 移动端搜索建议 -->
                    <div class="mobile-search-suggestions">
                        <!-- 热门搜索 -->
                        <div class="mobile-suggestions-section">
                            <h4 class="mobile-suggestions-title">热门搜索</h4>
                            <div class="mobile-suggestion-tags">
                                <span class="mobile-suggestion-tag">在线课程</span>
                                <span class="mobile-suggestion-tag">课程超市</span>
                                <span class="mobile-suggestion-tag">程序设计</span>
                                <span class="mobile-suggestion-tag">联机玩游戏</span>
                            </div>
                        </div>
                        <!-- 最近搜索 -->
                        <div class="mobile-suggestions-section">
                            <h4 class="mobile-suggestions-title">最近搜索</h4>
                            <ul class="mobile-recent-searches">
                                <li><a href="#" class="mobile-recent-search-item">在线课程</a></li>
                                <li><a href="#" class="mobile-recent-search-item">课程超市</a></li>
                                <li><a href="#" class="mobile-recent-search-item">数据分析</a></li>
                            </ul>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- 移动端搜索模态框结束 -->
        </div>
        <!-- 居中盒子结束部分-->
    </header>
    <!--第一区域结束部分-->

    <!-- 移动端搜索模态框 -->
    <div class="mobile-search-modal" id="mobileSearchModal">
        <!-- 模态框背景遮罩 -->
        <div class="modal-backdrop"></div>
        <!-- 模态框内容 -->
        <div class="modal-content">
            <!-- 模态框头部 -->
            <div class="modal-header">
                <h3 class="modal-title">搜索</h3>
                <!-- 关闭按钮 -->
                <button class="modal-close-btn" aria-label="关闭搜索">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <!-- 模态框搜索表单 -->
            <div class="modal-body">
                <form class="mobile-search-form">
                    <div class="mobile-search-input-wrapper">
                        <input
                            type="search"
                            class="mobile-search-input"
                            placeholder="搜索课程、文章、资源..."
                            aria-label="搜索内容"
                            autocomplete="off"
                        >
                        <button type="submit" class="mobile-search-submit-btn" aria-label="搜索">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                </form>

                <!-- 移动端搜索建议 -->
                <div class="mobile-search-suggestions">
                    <!-- 热门搜索 -->
                    <div class="mobile-suggestions-section">
                        <h4 class="mobile-suggestions-title">热门搜索</h4>
                        <div class="mobile-suggestion-tags">
                            <span class="mobile-suggestion-tag">JavaScript</span>
                            <span class="mobile-suggestion-tag">Python</span>
                            <span class="mobile-suggestion-tag">React</span>
                            <span class="mobile-suggestion-tag">Vue.js</span>
                            <span class="mobile-suggestion-tag">Node.js</span>
                            <span class="mobile-suggestion-tag">CSS</span>
                        </div>
                    </div>

                    <!-- 最近搜索 -->
                    <div class="mobile-suggestions-section">
                        <h4 class="mobile-suggestions-title">最近搜索</h4>
                        <ul class="mobile-recent-searches">
                            <li><a href="#" class="mobile-recent-search-item">前端开发教程</a></li>
                            <li><a href="#" class="mobile-recent-search-item">数据结构与算法</a></li>
                            <li><a href="#" class="mobile-recent-search-item">机器学习入门</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 移动端搜索模态框结束 -->
    <!-- 第一区域：网站导航条部分-->
    <!-- 主要内容区域开始 -->
    <main class="main-content">
        <!-- 英雄区域：网站主要介绍部分 -->
        <?php if (isSectionVisible($sections, 'hero')): ?>
        <section class="hero-section">
            <!-- 英雄区域容器，用于居中内容 -->
            <div class="container">
                <!-- 英雄内容区域 -->
                <div class="hero-content">
                    <!-- 文本内容区域 -->
                    <div class="hero-text">
                        <!-- 主标题 -->
                        <h1 class="hero-title"><?php echo escapeHtml($hero['title'] ?? '欢迎访问比特熊极简门户网站'); ?></h1>
                        <!-- 副标题 -->
                        <h2 class="hero-subtitle"><?php echo escapeHtml($hero['subtitle'] ?? '在这里一起和小熊彼彼度过美好的时光'); ?></h2>
                        <!-- 描述段落 -->
                        <p class="hero-description">
                            <?php echo escapeHtml($hero['description'] ?? '也许你会好奇，这个网站是干嘛的？比特熊极简门户网站是比特熊爱生活团队开发的一个综合应用型社交网站，你可以在这里学习、加入在线联机游戏、分享生活中的美好事物、或者一起学习，读书等'); ?>
                        </p>
                        <!-- 行动按钮区域 -->
                        <div class="hero-actions">
                            <!-- 主要行动按钮 -->
                            <a href="<?php echo escapeUrl($hero['primary_button_url'] ?? '#'); ?>" class="btn btn-primary"><?php echo escapeHtml($hero['primary_button_text'] ?? '立即体验'); ?></a>
                            <!-- 次要行动按钮 -->
                            <a href="<?php echo escapeUrl($hero['secondary_button_url'] ?? '#features'); ?>" class="btn btn-secondary"><?php echo escapeHtml($hero['secondary_button_text'] ?? '了解更多'); ?></a>
                        </div>
                    </div>
                    <!-- 图片区域 -->
                    <div class="hero-image">
                        <!-- 主要展示图片 -->
                        <img src="<?php echo escapeUrl($hero['hero_image'] ?? 'image/bitlogo.png'); ?>" alt="比特熊Logo" class="hero-img">
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- 特色功能区域 -->
        <section id="features" class="features-section">
            <!-- 特色功能容器 -->
            <div class="container">
                <!-- 区域标题 -->
                <div class="section-header">
                    <h2 class="section-title">我们的特色功能</h2>
                    <p class="section-subtitle">发现比特熊为您提供的丰富功能和服务</p>
                </div>
                <!-- 功能卡片网格 -->
                <div class="features-grid">
                    <!-- 功能卡片1：学习中心 -->
                    <div class="feature-card">
                        <!-- 功能图标 -->
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <!-- 学习图标SVG路径 -->
                                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <!-- 功能标题 -->
                        <h3 class="feature-title">在线学习</h3>
                        <!-- 功能描述 -->
                        <p class="feature-description">丰富的学习资源和课程，与志同道合的朋友一起学习成长</p>
                    </div>

                    <!-- 功能卡片2：游戏中心 -->
                    <div class="feature-card">
                        <!-- 功能图标 -->
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <!-- 游戏图标SVG路径 -->
                                <path d="M6 12H8M16 12H18M9 16H15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <!-- 功能标题 -->
                        <h3 class="feature-title">在线游戏</h3>
                        <!-- 功能描述 -->
                        <p class="feature-description">多人在线联机游戏，与朋友们一起享受游戏的乐趣</p>
                    </div>

                    <!-- 功能卡片3：社区交流 -->
                    <div class="feature-card">
                        <!-- 功能图标 -->
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <!-- 社区图标SVG路径 -->
                                <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <!-- 功能标题 -->
                        <h3 class="feature-title">社区交流</h3>
                        <!-- 功能描述 -->
                        <p class="feature-description">活跃的社区环境，分享生活中的美好事物和经验</p>
                    </div>

                    <!-- 功能卡片4：资源共享 -->
                    <div class="feature-card">
                        <!-- 功能图标 -->
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <!-- 资源共享图标SVG路径 -->
                                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <!-- 功能标题 -->
                        <h3 class="feature-title">资源共享</h3>
                        <!-- 功能描述 -->
                        <p class="feature-description">丰富的资源库，分享和获取有价值的学习资料</p>
                    </div>

                    <!-- 功能卡片5：共同学习 -->
                    <div class="feature-card">
                        <!-- 功能图标 -->
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <!-- 资源共享图标SVG路径 -->
                                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <!-- 功能标题 -->
                        <h3 class="feature-title">共同学习</h3>
                        <!-- 功能描述 -->
                        <p class="feature-description">在这里，我们可以一起学习中医学、社会学、心理学、计算机科学等知识</p>
                    </div>

                     <!-- 功能卡片6：小熊读书会 -->
                    <div class="feature-card">
                        <!-- 功能图标 -->
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <!-- 资源共享图标SVG路径 -->
                                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <!-- 功能标题 -->
                        <h3 class="feature-title">小熊读书会</h3>
                        <!-- 功能描述 -->
                        <p class="feature-description">小熊读书会是一群热爱阅读的小伙伴们自发组织的线上读书会，为大家提供一个共同学习的平台。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 统计数据区域 - O'Reilly风格美化 -->
        <?php if (isSectionVisible($sections, 'stats')): ?>
        <section class="stats-section">
            <!-- 统计数据容器 -->
            <div class="container">
                <!-- 区域标题 -->
                <div class="stats-header">
                    <h2 class="stats-title">Platform Impact</h2>
                    <p class="stats-subtitle">Real-time metrics showcasing our learning community's growth and engagement</p>
                </div>

                <!-- 统计数据网格 -->
                <div class="stats-grid">
                    <!-- 统计项1：用户数量 -->
                    <div class="stat-item">
                        <div class="stat-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke="currentColor" stroke-width="2"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="stat-number" data-stat="currentUsers">12,847</div>
                        <div class="stat-label">Active Learners</div>
                    </div>

                    <!-- 统计项2：项目数量 -->
                    <div class="stat-item">
                        <div class="stat-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                                <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                                <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="stat-number" data-stat="projectCount">2,500+</div>
                        <div class="stat-label">Learning Resources</div>
                    </div>

                    <!-- 统计项3：社区帖子 -->
                    <div class="stat-item">
                        <div class="stat-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
                                <line x1="9" y1="9" x2="15" y2="9" stroke="currentColor" stroke-width="2"/>
                                <line x1="9" y1="13" x2="15" y2="13" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="stat-number" data-stat="postCount">45,892</div>
                        <div class="stat-label">Community Posts</div>
                    </div>

                    <!-- 统计项4：在线时长 -->
                    <div class="stat-item">
                        <div class="stat-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="stat-number" data-stat="onlineHours">2,847,392</div>
                        <div class="stat-label">Learning Hours</div>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- O'Reilly风格技能建设区域 - 按照参考图重新设计 -->
        <?php if (isSectionVisible($sections, 'oreilly-hero')): ?>
        <section class="oreilly-hero-section">
            <div class="container">
                <div class="hero-main-content">
                    <!-- 左侧大圆形和蝴蝶图标 -->
                    <div class="hero-visual-area">
                        <div class="large-circle">
                            <!-- 蝴蝶SVG图标 -->
                            <svg class="butterfly-svg" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                                <!-- 蝴蝶身体 -->
                                <ellipse cx="60" cy="60" rx="2.5" ry="30" fill="url(#bodyGradient)"/>

                                <!-- 左上翅膀 -->
                                <path d="M57 45 Q35 25, 20 12 Q15 8, 20 6 Q35 3, 50 18 Q55 30, 57 42 Z"
                                      fill="url(#wingGradient1)"/>

                                <!-- 右上翅膀 -->
                                <path d="M63 45 Q85 25, 100 12 Q105 8, 100 6 Q85 3, 70 18 Q65 30, 63 42 Z"
                                      fill="url(#wingGradient2)"/>

                                <!-- 左下翅膀 -->
                                <path d="M57 75 Q45 88, 35 98 Q30 103, 35 105 Q45 108, 55 98 Q57 88, 57 78 Z"
                                      fill="url(#wingGradient3)"/>

                                <!-- 右下翅膀 -->
                                <path d="M63 75 Q75 88, 85 98 Q90 103, 85 105 Q75 108, 65 98 Q63 88, 63 78 Z"
                                      fill="url(#wingGradient4)"/>

                                <!-- 触角 -->
                                <path d="M55 42 Q50 32, 45 28" stroke="#3b82f6" stroke-width="2" fill="none"/>
                                <path d="M65 42 Q70 32, 75 28" stroke="#3b82f6" stroke-width="2" fill="none"/>
                                <circle cx="45" cy="28" r="2" fill="#60a5fa"/>
                                <circle cx="75" cy="28" r="2" fill="#60a5fa"/>

                                <!-- 渐变定义 -->
                                <defs>
                                    <linearGradient id="bodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
                                    </linearGradient>
                                    <linearGradient id="wingGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:0.9" />
                                        <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.7" />
                                    </linearGradient>
                                    <linearGradient id="wingGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
                                        <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:0.9" />
                                    </linearGradient>
                                    <linearGradient id="wingGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#93c5fd;stop-opacity:0.7" />
                                        <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:0.8" />
                                    </linearGradient>
                                    <linearGradient id="wingGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:0.8" />
                                        <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.6" />
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        <!-- 右上角小白圆点 -->
                        <div class="white-dot"></div>
                    </div>

                    <!-- 右侧文本内容 -->
                    <div class="hero-text-content">
                        <h1 class="hero-main-title"><?php echo escapeHtml($oreillyHero['main_title'] ?? 'Build the skills your teams need'); ?></h1>
                        <p class="hero-description">
                            <?php echo $oreillyHero['description'] ?? 'Give your teams the O\'Reilly learning platform and equip them with the resources that drive business outcomes. <strong>Click on a feature below to explore.</strong>'; ?>
                        </p>
                        <div class="hero-action-buttons">
                            <a href="<?php echo escapeHtml($oreillyHero['primary_button_url'] ?? '#'); ?>" class="btn-request-demo"><?php echo escapeHtml($oreillyHero['primary_button_text'] ?? 'Request a demo ›'); ?></a>
                            <a href="<?php echo escapeHtml($oreillyHero['secondary_button_url'] ?? '#'); ?>" class="btn-try-free"><?php echo escapeHtml($oreillyHero['secondary_button_text'] ?? 'Try it free ›'); ?></a>
                        </div>
                    </div>
                </div>

                <!-- 底部功能导航栏 -->
                <div class="features-navigation-bar">
                    <div class="feature-nav-item active" data-feature="trusted-content">
                        <div class="feature-nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="feature-nav-label">Trusted content</span>
                    </div>

                    <div class="feature-nav-item" data-feature="live-events">
                        <div class="feature-nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="feature-nav-label">Live online events</span>
                    </div>

                    <div class="feature-nav-item" data-feature="courses">
                        <div class="feature-nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="feature-nav-label">Courses</span>
                    </div>

                    <div class="feature-nav-item" data-feature="interactive">
                        <div class="feature-nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                                <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                                <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="feature-nav-label">Interactive learning</span>
                    </div>

                    <div class="feature-nav-item" data-feature="certification">
                        <div class="feature-nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 1 1-18 0 9 9 0 0 1 18 0z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="feature-nav-label">Certification prep</span>
                    </div>

                    <div class="feature-nav-item" data-feature="answers">
                        <div class="feature-nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
                                <line x1="9" y1="9" x2="15" y2="9" stroke="currentColor" stroke-width="2"/>
                                <line x1="9" y1="13" x2="15" y2="13" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="feature-nav-label">O'Reilly Answers</span>
                    </div>

                    <div class="feature-nav-item" data-feature="ai-academy">
                        <div class="feature-nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="feature-nav-label">AI Academy</span>
                    </div>

                    <div class="feature-nav-item" data-feature="assignments">
                        <div class="feature-nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M9 11H15M9 15H15M17 21L20 18H18a2 2 0 0 1-2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h2l3 3z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="feature-nav-label">Assignments</span>
                    </div>

                    <div class="feature-nav-item" data-feature="insights">
                        <div class="feature-nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M3 3v18h18" stroke="currentColor" stroke-width="2"/>
                                <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="feature-nav-label">Insights Dashboard</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- O'Reilly风格专家课程区域 -->
        <section class="expert-courses-section">
            <div class="container">
                <div class="courses-grid">
                    <!-- 左侧：专家指导课程 -->
                    <div class="course-item">
                        <h3 class="course-title"><?php echo $oreillyCourses['live_courses']['title'] ?? 'Level up with<br>expert-led live courses'; ?></h3>
                        <p class="course-description">
                            <?php echo escapeHtml($oreillyCourses['live_courses']['description'] ?? 'Reserve your seat for interactive workshops to gain hands-on experience—and ask questions along the way.'); ?>
                        </p>
                        <a href="<?php echo escapeHtml($oreillyCourses['live_courses']['button_url'] ?? '#'); ?>" class="course-btn"><?php echo escapeHtml($oreillyCourses['live_courses']['button_text'] ?? 'Pick your events ›'); ?></a>
                    </div>

                    <!-- 右侧：AI智能答案 -->
                    <div class="course-item">
                        <h3 class="course-title"><?php echo $oreillyCourses['ai_answers']['title'] ?? 'O\'Reilly AI-powered Answers<br>just got even smarter'; ?></h3>
                        <p class="course-description">
                            <?php echo escapeHtml($oreillyCourses['ai_answers']['description'] ?? 'O\'Reilly Answers instantly generates information teams can trust, sourced from thousands of titles on our learning platform.'); ?>
                        </p>
                        <a href="<?php echo escapeHtml($oreillyCourses['ai_answers']['button_url'] ?? '#'); ?>" class="course-btn"><?php echo escapeHtml($oreillyCourses['ai_answers']['button_text'] ?? 'Discover Answers ›'); ?></a>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- O'Reilly风格专家展示区域 -->
        <?php if (isSectionVisible($sections, 'experts')): ?>
        <section class="experts-section">
            <div class="container">
                <div class="experts-header">
                    <h2 class="experts-title"><?php echo $oreillyExperts['main_title'] ?? 'We share the knowledge of<br>innovators. You put it to work.'; ?></h2>
                    <p class="experts-description">
                        <?php echo escapeHtml($oreillyExperts['description'] ?? 'Tech teams love tapping into the minds of innovators through our expert-led courses, renowned text-based content, and bite-size online Superstream tech conferences. In fact, in a recent survey, one-third of tech practitioners rated O\'Reilly content a five out of five (excellent)—better than Pluralsight, LinkedIn Learning, Udacity, or Skillsoft.'); ?>
                    </p>
                </div>

                <!-- 动态专家卡片网格 -->
                <div class="experts-grid" id="expertsGrid">
                    <!-- 专家卡片将通过JavaScript动态生成 -->
                </div>
            </div>
        </section>
        <?php endif; ?>


        <!-- 紧凑型推荐视频区域 -->
        <?php if (isSectionVisible($sections, 'testimonial')): ?>
        <section class="compact-testimonial-section">
            <div class="container">
                <div class="compact-testimonial-content">
                    <!-- 左侧：视频区域 -->
                    <div class="compact-video-container">
                        <div class="compact-video-wrapper">
                            <div class="compact-play-button">
                                <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                                    <circle cx="24" cy="24" r="24" fill="rgba(0,0,0,0.8)"/>
                                    <path d="M19 14L33 24L19 34V14Z" fill="white"/>
                                </svg>
                            </div>
                            <div class="compact-video-overlay">
                                <div class="compact-speaker-info">
                                    <span class="compact-speaker-name"><?php echo escapeHtml($video['speaker_name'] ?? 'Jose Dunio'); ?></span>
                                    <span class="compact-speaker-role"><?php echo escapeHtml($video['speaker_role'] ?? 'Principal Software Engineer'); ?></span>
                                </div>
                                <div class="compact-company-badge"><?php echo escapeHtml($video['company_badge'] ?? 'O\'REILLY'); ?></div>
                            </div>
                        </div>
                        <!-- 视频导航点 -->
                        <div class="compact-video-dots">
                            <span class="compact-dot active"></span>
                            <span class="compact-dot"></span>
                            <span class="compact-dot"></span>
                        </div>
                    </div>

                    <!-- 右侧：详情信息 -->
                    <div class="compact-testimonial-details">
                        <div class="compact-testimonial-header">
                            <div class="compact-rating">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M10 1L12.5 7.5L19 7.5L14 12L16 19L10 15L4 19L6 12L1 7.5L7.5 7.5L10 1Z" fill="#fbbf24"/>
                                </svg>
                                <span class="compact-rating-text">Featured Testimonial</span>
                            </div>
                        </div>
                        <h3 class="compact-testimonial-title"><?php echo escapeHtml($oreillyTestimonial['title'] ?? 'Why Jose uses O\'Reilly every day'); ?></h3>
                        <p class="compact-testimonial-description">
                            "<?php echo escapeHtml($oreillyTestimonial['description'] ?? 'As a principal software engineer, I rely on O\'Reilly\'s platform to keep my team updated with the latest technologies and best practices.'); ?>"
                        </p>
                        <div class="compact-testimonial-stats">
                            <div class="compact-stat">
                                <span class="compact-stat-number"><?php echo escapeHtml($oreillyTestimonial['stat1_number'] ?? '5+'); ?></span>
                                <span class="compact-stat-label"><?php echo escapeHtml($oreillyTestimonial['stat1_label'] ?? 'Years using'); ?></span>
                            </div>
                            <div class="compact-stat">
                                <span class="compact-stat-number"><?php echo escapeHtml($oreillyTestimonial['stat2_number'] ?? '200+'); ?></span>
                                <span class="compact-stat-label"><?php echo escapeHtml($oreillyTestimonial['stat2_label'] ?? 'Books read'); ?></span>
                            </div>
                            <div class="compact-stat">
                                <span class="compact-stat-number"><?php echo escapeHtml($oreillyTestimonial['stat3_number'] ?? '50+'); ?></span>
                                <span class="compact-stat-label"><?php echo escapeHtml($oreillyTestimonial['stat3_label'] ?? 'Courses completed'); ?></span>
                            </div>
                        </div>
                        <a href="<?php echo escapeHtml($oreillyTestimonial['button_url'] ?? '#'); ?>" class="compact-testimonial-btn">
                            <?php echo escapeHtml($oreillyTestimonial['button_text'] ?? 'View more testimonials ›'); ?>
                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                                <path d="M5 3L9 7L5 11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- O'Reilly风格底部行动号召区域 -->
        <section class="oreilly-cta-section">
            <div class="container">
                <div class="oreilly-cta-content">
                    <h2 class="oreilly-cta-title"><?php echo escapeHtml($oreillyCta['main_title'] ?? 'See how O\'Reilly can help your tech teams stay ahead'); ?></h2>
                    <div class="oreilly-cta-actions">
                        <a href="<?php echo escapeHtml($oreillyCta['primary_button_url'] ?? '#'); ?>" class="oreilly-btn oreilly-btn-primary">
                            <?php echo escapeHtml($oreillyCta['primary_button_text'] ?? 'Request a demo'); ?>
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                        <a href="<?php echo escapeHtml($oreillyCta['secondary_button_url'] ?? '#'); ?>" class="oreilly-btn oreilly-btn-secondary">
                            <?php echo escapeHtml($oreillyCta['secondary_button_text'] ?? 'Try it free'); ?>
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>
    </main>

    <!-- 页面底部 -->
    <footer class="footer">
        <!-- 底部容器 -->
        <div class="container">
            <!-- 底部内容网格 -->
            <div class="footer-content">
                <!-- 底部左侧：品牌信息 -->
                <div class="footer-brand">
                    <!-- 品牌Logo -->
                    <img src="image/bit.png" alt="比特熊Logo" class="footer-logo">
                    <!-- 品牌描述 -->
                    <p class="footer-description">
                        比特熊极简门户网站致力于为用户提供优质的在线学习、游戏和社交体验。
                    </p>
                    <!-- 社交媒体链接 -->
                    <div class="social-links">
                        <a href="#" class="social-link" aria-label="微信">
                            <!-- 微信图标 -->
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8.5 12.5c0 .8-.7 1.5-1.5 1.5s-1.5-.7-1.5-1.5.7-1.5 1.5-1.5 1.5.7 1.5 1.5zm7 0c0 .8-.7 1.5-1.5 1.5s-1.5-.7-1.5-1.5.7-1.5 1.5-1.5 1.5.7 1.5 1.5z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" aria-label="QQ">
                            <!-- QQ图标 -->
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- 底部右侧：快速链接 -->
                <div class="footer-links">
                    <!-- 链接组1：产品 -->
                    <div class="link-group">
                        <h4 class="link-group-title">产品</h4>
                        <ul class="link-list">
                            <li><a href="#" class="footer-link">在线学习</a></li>
                            <li><a href="#" class="footer-link">游戏中心</a></li>
                            <li><a href="#" class="footer-link">社区论坛</a></li>
                            <li><a href="#" class="footer-link">资源库</a></li>
                        </ul>
                    </div>

                    <!-- 链接组2：支持 -->
                    <div class="link-group">
                        <h4 class="link-group-title">支持</h4>
                        <ul class="link-list">
                            <li><a href="#" class="footer-link">帮助中心</a></li>
                            <li><a href="#" class="footer-link">联系我们</a></li>
                            <li><a href="#" class="footer-link">意见反馈</a></li>
                            <li><a href="#" class="footer-link">服务条款</a></li>
                        </ul>
                    </div>

                    <!-- 链接组3：关于 -->
                    <div class="link-group">
                        <h4 class="link-group-title">关于我们</h4>
                        <ul class="link-list">
                            <li><a href="#" class="footer-link">团队介绍</a></li>
                            <li><a href="#" class="footer-link">发展历程</a></li>
                            <li><a href="#" class="footer-link">加入我们</a></li>
                            <li><a href="#" class="footer-link">隐私政策</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 版权信息 -->
            <div class="footer-bottom">
                <p class="copyright">
                    © 2024 比特熊爱生活团队. 保留所有权利.
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript脚本区域 -->
    <script>
        // 页面加载完成后初始化所有功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端导航菜单
            initMobileMenu();
            // 初始化下拉菜单
            initDropdownMenus();
            // 初始化搜索功能
            initSearchFunction();
            // 初始化其他功能
            initOtherFeatures();
        });

        // 处理退出登录
        async function handleLogout(event) {
            event.preventDefault();

            if (!confirm('确定要退出登录吗？')) {
                return;
            }

            try {
                const response = await fetch('api/logout.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // 退出成功，刷新页面
                    window.location.reload();
                } else {
                    alert('退出登录失败：' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('退出登录错误:', error);
                alert('网络错误，请稍后重试');
            }
        }

        // 移动端导航菜单切换功能
        function initMobileMenu() {
            // 获取导航菜单元素
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const navMenu = document.querySelector('.list-left');
            const overlay = document.querySelector('.mobile-menu-overlay');

            // 如果移动菜单按钮存在，添加点击事件
            if (mobileMenuBtn && navMenu) {
                mobileMenuBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    // 切换导航菜单的显示状态
                    const isActive = navMenu.classList.toggle('active');
                    // 切换按钮的激活状态
                    this.classList.toggle('active');
                    // 切换遮罩层状态
                    if (overlay) {
                        overlay.classList.toggle('active', isActive);
                    }
                    // 防止页面滚动
                    document.body.style.overflow = isActive ? 'hidden' : '';
                });

                // 点击遮罩层关闭菜单
                if (overlay) {
                    overlay.addEventListener('click', function() {
                        closeMenu();
                    });
                }

                // 点击菜单外部关闭菜单
                document.addEventListener('click', function(e) {
                    if (!navMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                        closeMenu();
                    }
                });

                // 阻止菜单内部点击事件冒泡
                navMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });

                // 关闭菜单的统一函数
                function closeMenu() {
                    navMenu.classList.remove('active');
                    mobileMenuBtn.classList.remove('active');
                    if (overlay) {
                        overlay.classList.remove('active');
                    }
                    document.body.style.overflow = '';
                }

                // 窗口大小改变时关闭移动菜单
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        closeMenu();
                    }
                });

                // ESC键关闭菜单
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && navMenu.classList.contains('active')) {
                        closeMenu();
                    }
                });
            }
        }

        // 下拉菜单功能
        function initDropdownMenus() {
            // 获取所有下拉菜单项
            const dropdownItems = document.querySelectorAll('.nav-item.dropdown');

            // 检测是否为移动设备
            function isMobile() {
                return window.innerWidth <= 768;
            }

            dropdownItems.forEach(item => {
                const dropdownToggle = item.querySelector('.dropdown-toggle');
                const dropdownMenu = item.querySelector('.dropdown-menu');
                const dropdownArrow = item.querySelector('.dropdown-arrow');

                // 桌面端：鼠标悬停显示下拉菜单
                item.addEventListener('mouseenter', function() {
                    if (!isMobile()) {
                        dropdownMenu.classList.add('show');
                        dropdownArrow.classList.add('rotated');
                    }
                });

                // 桌面端：鼠标离开隐藏下拉菜单
                item.addEventListener('mouseleave', function() {
                    if (!isMobile()) {
                        dropdownMenu.classList.remove('show');
                        dropdownArrow.classList.remove('rotated');
                    }
                });

                // 点击切换下拉菜单（移动端和桌面端）
                dropdownToggle.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 在移动端或者桌面端都允许点击切换
                    // 关闭其他打开的下拉菜单
                    dropdownItems.forEach(otherItem => {
                        if (otherItem !== item) {
                            otherItem.querySelector('.dropdown-menu').classList.remove('show');
                            otherItem.querySelector('.dropdown-arrow').classList.remove('rotated');
                        }
                    });

                    // 切换当前下拉菜单
                    dropdownMenu.classList.toggle('show');
                    dropdownArrow.classList.toggle('rotated');
                });
            });

            // 点击页面其他地方关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.nav-item.dropdown')) {
                    dropdownItems.forEach(item => {
                        item.querySelector('.dropdown-menu').classList.remove('show');
                        item.querySelector('.dropdown-arrow').classList.remove('rotated');
                    });
                }
            });
        }

        // 搜索功能
        function initSearchFunction() {
            // 桌面端搜索元素
            const searchInput = document.querySelector('.search-input');
            const searchForm = document.querySelector('.search-form');
            const searchSuggestions = document.querySelector('.search-suggestions');
            const suggestionTags = document.querySelectorAll('.suggestion-tag');
            const recentSearchItems = document.querySelectorAll('.recent-search-item');

            // 移动端搜索元素
            const mobileSearchBtn = document.querySelector('.mobile-search-btn');
            const mobileSearchModal = document.querySelector('.mobile-search-modal');
            const modalCloseBtn = document.querySelector('.modal-close-btn');
            const modalBackdrop = document.querySelector('.modal-backdrop');
            const mobileSearchForm = document.querySelector('.mobile-search-form');
            const mobileSearchInput = document.querySelector('.mobile-search-input');
            const mobileSuggestionTags = document.querySelectorAll('.mobile-suggestion-tag');
            const mobileRecentSearchItems = document.querySelectorAll('.mobile-recent-search-item');

            // 移动端搜索按钮点击事件
            if (mobileSearchBtn) {
                mobileSearchBtn.addEventListener('click', function() {
                    mobileSearchModal.classList.add('show');
                    document.body.classList.add('modal-open');
                    // 自动聚焦到搜索输入框
                    setTimeout(() => {
                        mobileSearchInput.focus();
                    }, 100);
                });
            }

            // 关闭移动端搜索模态框
            function closeMobileSearchModal() {
                mobileSearchModal.classList.remove('show');
                document.body.classList.remove('modal-open');
            }

            // 模态框关闭按钮点击事件
            if (modalCloseBtn) {
                modalCloseBtn.addEventListener('click', closeMobileSearchModal);
            }

            // 点击背景遮罩关闭模态框
            if (modalBackdrop) {
                modalBackdrop.addEventListener('click', closeMobileSearchModal);
            }

            // ESC键关闭模态框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && mobileSearchModal.classList.contains('show')) {
                    closeMobileSearchModal();
                }
            });

            // 桌面端搜索功能
            if (searchInput && searchForm && searchSuggestions) {
                // 搜索输入框获得焦点时显示建议
                searchInput.addEventListener('focus', function() {
                    searchSuggestions.classList.add('show');
                });

                // 搜索输入框失去焦点时隐藏建议（延迟执行，允许点击建议项）
                searchInput.addEventListener('blur', function() {
                    setTimeout(() => {
                        searchSuggestions.classList.remove('show');
                    }, 200);
                });
            }

            // 桌面端搜索输入框内容变化时的处理
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const query = this.value.trim();
                    if (query.length > 0) {
                        // 这里可以添加实时搜索建议的逻辑
                        console.log('桌面端搜索查询:', query);
                        // 可以调用API获取搜索建议
                        // fetchSearchSuggestions(query);
                    }
                });

                // 键盘导航支持
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        // ESC键关闭搜索建议
                        searchSuggestions.classList.remove('show');
                        this.blur();
                    }
                });
            }

            // 桌面端搜索表单提交处理
            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const query = searchInput.value.trim();
                    if (query) {
                        // 执行搜索
                        performSearch(query);
                    }
                });
            }

            // 移动端搜索表单提交处理
            if (mobileSearchForm) {
                mobileSearchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const query = mobileSearchInput.value.trim();
                    if (query) {
                        // 执行搜索
                        performSearch(query);
                        // 关闭模态框
                        closeMobileSearchModal();
                    }
                });
            }

            // 移动端搜索输入框内容变化时的处理
            if (mobileSearchInput) {
                mobileSearchInput.addEventListener('input', function() {
                    const query = this.value.trim();
                    if (query.length > 0) {
                        console.log('移动端搜索查询:', query);
                    }
                });
            }

            // 桌面端点击建议标签
            suggestionTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    const tagText = this.textContent;
                    if (searchInput) {
                        searchInput.value = tagText;
                    }
                    performSearch(tagText);
                });
            });

            // 移动端点击建议标签
            mobileSuggestionTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    const tagText = this.textContent;
                    if (mobileSearchInput) {
                        mobileSearchInput.value = tagText;
                    }
                    performSearch(tagText);
                    closeMobileSearchModal();
                });
            });

            // 桌面端点击最近搜索项
            recentSearchItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const searchText = this.textContent;
                    if (searchInput) {
                        searchInput.value = searchText;
                    }
                    performSearch(searchText);
                });
            });

            // 移动端点击最近搜索项
            mobileRecentSearchItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const searchText = this.textContent;
                    if (mobileSearchInput) {
                        mobileSearchInput.value = searchText;
                    }
                    performSearch(searchText);
                    closeMobileSearchModal();
                });
            });



            // 桌面端点击建议标签
            suggestionTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    const tagText = this.textContent;
                    if (searchInput) {
                        searchInput.value = tagText;
                    }
                    performSearch(tagText);
                });
            });

            // 移动端点击建议标签
            mobileSuggestionTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    const tagText = this.textContent;
                    if (mobileSearchInput) {
                        mobileSearchInput.value = tagText;
                    }
                    performSearch(tagText);
                    closeMobileSearchModal();
                });
            });

            // 桌面端点击最近搜索项
            recentSearchItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const searchText = this.textContent;
                    if (searchInput) {
                        searchInput.value = searchText;
                    }
                    performSearch(searchText);
                });
            });

            // 移动端点击最近搜索项
            mobileRecentSearchItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const searchText = this.textContent;
                    if (mobileSearchInput) {
                        mobileSearchInput.value = searchText;
                    }
                    performSearch(searchText);
                    closeMobileSearchModal();
                });
            });
        }

        // 执行搜索功能
        function performSearch(query) {
            console.log('执行搜索:', query);
            // 这里添加实际的搜索逻辑
            // 可以跳转到搜索结果页面或显示搜索结果

            // 示例：显示搜索结果（实际项目中应该跳转到搜索页面）
            alert(`搜索: "${query}"\n\n这里将显示搜索结果页面`);

            // 添加到最近搜索（实际项目中应该保存到localStorage或服务器）
            addToRecentSearches(query);
        }

        // 添加到最近搜索
        function addToRecentSearches(query) {
            // 这里可以实现将搜索记录保存到localStorage
            let recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');

            // 移除重复项
            recentSearches = recentSearches.filter(item => item !== query);

            // 添加到开头
            recentSearches.unshift(query);

            // 限制最多保存10条记录
            recentSearches = recentSearches.slice(0, 10);

            // 保存到localStorage
            localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
        }

        // 动态数据更新功能
        function initDynamicStats() {
            // 如果是PHP页面，则使用PHP传递的数据
            if (typeof window.siteData !== 'undefined') {
                updateStatsDisplay(window.siteData);
                return;
            }

            // 否则通过API获取数据
            loadStatsFromAPI();

            // 设置定时更新（每5分钟）
            setInterval(loadStatsFromAPI, 5 * 60 * 1000);
        }

        // 从API加载统计数据
        function loadStatsFromAPI() {
            // 检查是否在本地文件环境中运行
            if (window.location.protocol === 'file:') {
                console.log('在本地文件环境中运行，跳过API请求');
                return;
            }

            fetch('api/stats.php')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('API响应错误');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        updateStatsDisplay(data.data);
                    } else {
                        console.error('获取统计数据失败:', data.error);
                    }
                })
                .catch(error => {
                    console.log('API请求失败，使用默认数据:', error);
                    // 使用默认的静态数据
                    useDefaultStats();
                });
        }

        // 使用默认统计数据
        function useDefaultStats() {
            const defaultData = {
                currentUsers: { formatted: '12,847' },
                projectCount: { formatted: '2,500+' },
                postCount: { formatted: '45,892' },
                onlineHours: { formatted: '2,847,392' }
            };
            updateStatsDisplay(defaultData);
        }

        // 更新统计数据显示
        function updateStatsDisplay(data) {
            // 更新各个统计项
            const statElements = document.querySelectorAll('[data-stat]');

            statElements.forEach(element => {
                const statType = element.getAttribute('data-stat');

                if (data[statType]) {
                    const newValue = data[statType].formatted || data[statType];

                    // 如果值发生变化，添加动画效果
                    if (element.textContent !== newValue) {
                        element.classList.add('stat-updating');

                        setTimeout(() => {
                            element.textContent = newValue;
                            element.classList.remove('stat-updating');
                            element.classList.add('stat-updated');

                            setTimeout(() => {
                                element.classList.remove('stat-updated');
                            }, 1000);
                        }, 300);
                    }
                }
            });
        }

        // 手动刷新统计数据
        function refreshStats() {
            const refreshBtn = document.querySelector('.refresh-stats-btn');
            if (refreshBtn) {
                refreshBtn.textContent = '刷新中...';
                refreshBtn.disabled = true;
            }

            // 检查是否在本地文件环境中运行
            if (window.location.protocol === 'file:') {
                console.log('在本地文件环境中运行，使用默认数据');
                useDefaultStats();
                showNotification('数据已刷新（本地模式）', 'success');
                if (refreshBtn) {
                    refreshBtn.textContent = '刷新数据';
                    refreshBtn.disabled = false;
                }
                return;
            }

            fetch('api/stats.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('API响应错误');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    updateStatsDisplay(data.data);
                    showNotification('数据已更新', 'success');
                } else {
                    showNotification('更新失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.log('刷新失败，使用默认数据:', error);
                useDefaultStats();
                showNotification('使用默认数据', 'info');
            })
            .finally(() => {
                if (refreshBtn) {
                    refreshBtn.textContent = '刷新数据';
                    refreshBtn.disabled = false;
                }
            });
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;

            // 添加样式
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            // 设置背景色
            switch (type) {
                case 'success':
                    notification.style.backgroundColor = '#10b981';
                    break;
                case 'error':
                    notification.style.backgroundColor = '#ef4444';
                    break;
                default:
                    notification.style.backgroundColor = '#3b82f6';
            }

            // 添加到页面
            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 初始化其他功能
        function initOtherFeatures() {
            // 初始化动态统计数据
            initDynamicStats();
            // 平滑滚动到锚点
            const smoothScrollLinks = document.querySelectorAll('a[href^="#"]');
            smoothScrollLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        // 使用平滑滚动效果
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // 统计数字动画效果
            const observerOptions = {
                threshold: 0.5,
                rootMargin: '0px 0px -100px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 开始数字动画
                        animateNumbers(entry.target);
                        // 停止观察该元素
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // 观察所有统计数字元素
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                observer.observe(stat);
            });
        }

        // 数字动画函数
        function animateNumbers(element) {
            const finalNumber = element.textContent.replace(/[^\d]/g, '');
            const duration = 2000; // 动画持续时间
            const increment = finalNumber / (duration / 16); // 每帧增加的数值
            let currentNumber = 0;

            const timer = setInterval(() => {
                currentNumber += increment;
                if (currentNumber >= finalNumber) {
                    currentNumber = finalNumber;
                    clearInterval(timer);
                }

                // 格式化数字显示
                const formattedNumber = Math.floor(currentNumber).toLocaleString();
                const suffix = element.textContent.replace(/[\d,]/g, '');
                element.textContent = formattedNumber + suffix;
            }, 16);
        }

        // O'Reilly风格功能导航栏交互
        document.addEventListener('DOMContentLoaded', function() {
            const featureNavItems = document.querySelectorAll('.feature-nav-item');

            // 功能描述数据
            const featureDescriptions = {
                'trusted-content': {
                    title: '您可以信赖的精选内容',
                    description: '来自比特熊和近200个出版合作伙伴的60,000多个标题，包括哈佛商业评论、培生等。另外还有30,000多小时的视频、早期发布的书籍、专家创建的播放列表等。'
                },
                'live-events': {
                    title: '现场活动和培训课程',
                    description: '您的团队可以参加现场培训课程，与当今最知名的专家一起参加虚拟技术活动，并在过程中提出问题。'
                },
                'courses': {
                    title: '按需课程',
                    description: '您的团队可以在所需的所有技术和业务主题中进行自定进度学习，包括云计算、软件架构、基础设施和运维、编程语言、AI和ML、安全、批判性思维等。'
                },
                'interactive': {
                    title: '实验室和沙盒',
                    description: '通过交互式实验室和沙盒让您的团队更快上手。我们的专家指导和非结构化实时编码环境让他们可以在浏览器中安全地练习最受欢迎的技术和云平台——无需设置。'
                },
                'certification': {
                    title: '认证预备',
                    description: '您的团队可以直接获得官方预备材料以及行业中最受欢迎的技术认证的练习测试。'
                },
                'answers': {
                    title: '来自无与伦比内容的生成式AI响应',
                    description: '由AI驱动，比特熊答案可以即时提供可信信息，来源于我们平台上数千个顶级标题、课程和视频。'
                },
                'ai-academy': {
                    title: '帮助您的整个组织将GenAI投入工作',
                    description: '今天的每个员工都需要学习如何提示GenAI，使用它来增强批判性思维和生产力等。通过AI学院，他们可以做到这一点。成本更低。'
                },
                'assignments': {
                    title: '作业和课程策划',
                    description: '您可以保存、组织和分配来自书籍和章节、课程、实验室、现场在线活动等的内容——并跟踪每个学习者的进度。或创建关键内容的播放列表来构建课程与您的团队或组织分享。'
                },
                'insights': {
                    title: '洞察仪表板',
                    description: '了解的不仅仅是您的团队成员正在查看什么——知道他们是如何学习的。然后将他们的使用情况与您行业的竞争对手进行比较。'
                }
            };

            // 为每个导航项添加点击事件
            featureNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有活动状态
                    featureNavItems.forEach(navItem => {
                        navItem.classList.remove('active');
                    });

                    // 添加当前项的活动状态
                    this.classList.add('active');

                    // 获取功能数据
                    const featureKey = this.getAttribute('data-feature');
                    const featureData = featureDescriptions[featureKey];

                    if (featureData) {
                        // 这里可以添加显示功能详情的逻辑
                        console.log('选中功能:', featureData.title);

                        // 可以在这里添加显示功能详情面板的代码
                        // 例如：showFeatureDetails(featureData);
                    }
                });

                // 添加悬停效果增强
                item.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('active')) {
                        this.style.background = 'rgba(255, 255, 255, 0.06)';
                        this.style.transform = 'translateY(-2px)';
                    }
                });

                item.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.background = '';
                        this.style.transform = '';
                    }
                });
            });

            // 添加键盘导航支持
            document.addEventListener('keydown', function(e) {
                const activeItem = document.querySelector('.feature-nav-item.active');
                if (!activeItem) return;

                let nextItem = null;

                if (e.key === 'ArrowLeft') {
                    nextItem = activeItem.previousElementSibling;
                } else if (e.key === 'ArrowRight') {
                    nextItem = activeItem.nextElementSibling;
                }

                if (nextItem && nextItem.classList.contains('feature-nav-item')) {
                    nextItem.click();
                    e.preventDefault();
                }
            });

            // 添加触摸设备支持
            featureNavItems.forEach(item => {
                item.addEventListener('touchstart', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                item.addEventListener('touchend', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = '';
                    }
                });
            });
        });
    </script>

    <!-- AOS动画库JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // 专家数据（从PHP获取）
        const expertsData = <?php echo json_encode(array_map(function($expert) {
            return [
                'id' => $expert['id'] ?? 0,
                'name' => $expert['name'] ?? '',
                'title' => $expert['title'] ?? '',
                'image' => $expert['image'] ?? 'image/default-avatar.svg',
                'alt' => ($expert['name'] ?? '') . ' - ' . ($expert['title'] ?? ''),
                'description' => $expert['description'] ?? ''
            ];
        }, $experts)); ?>;

        // 动态生成专家卡片
        function generateExpertCards() {
            const expertsGrid = document.getElementById('expertsGrid');
            if (!expertsGrid) return;

            expertsGrid.innerHTML = '';

            expertsData.forEach((expert, index) => {
                const expertCard = document.createElement('div');
                expertCard.className = 'expert-card';
                expertCard.setAttribute('data-aos', 'fade-up');
                expertCard.setAttribute('data-aos-delay', index * 100);

                expertCard.innerHTML = `
                    <div class="expert-image-container">
                        <img src="${expert.image}" alt="${expert.alt}" class="expert-image">
                        <div class="expert-overlay"></div>
                    </div>
                    <div class="expert-info">
                        <h4 class="expert-name">${expert.name}</h4>
                        <p class="expert-title">${expert.title}</p>
                    </div>
                `;

                expertsGrid.appendChild(expertCard);
            });
        }



        // 页面加载完成后生成专家卡片
        document.addEventListener('DOMContentLoaded', function() {
            generateExpertCards();

            // 初始化AOS动画
            AOS.init({
                duration: 800,
                easing: 'ease-out-cubic',
                once: true,
                offset: 100
            });

            // 检查动态导航栏
            const dynamicNavbar = document.getElementById('dynamicNavbar');
            if (dynamicNavbar) {
                const navItems = dynamicNavbar.querySelectorAll('.nav-item');
                console.log(`🧭 动态导航栏已加载，包含 ${navItems.length} 个菜单项`);

                // 统计下拉菜单
                const dropdownItems = dynamicNavbar.querySelectorAll('.dropdown-item');
                console.log(`📋 包含 ${dropdownItems.length} 个下拉菜单项`);
            }
        });
    </script>

    <!-- 动态导航栏提示 -->
    <div id="navbarUpdateNotice" style="
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        font-size: 0.875rem;
        max-width: 300px;
        z-index: 10000;
        display: none;
        animation: slideIn 0.3s ease;
    ">
        <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
            <span style="font-size: 1.2rem;">🧭</span>
            <strong>导航栏已动态化</strong>
        </div>
        <p style="margin: 0; opacity: 0.9; line-height: 1.4;">
            导航栏现在从后台数据库动态加载，可在管理后台进行自定义配置。
        </p>
        <div style="margin-top: 0.75rem; display: flex; gap: 0.5rem;">
            <a href="admin-dashboard.php" style="
                background: rgba(255, 255, 255, 0.2);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 6px;
                text-decoration: none;
                font-size: 0.8rem;
                transition: background 0.2s ease;
            ">管理后台</a>
            <button onclick="this.parentElement.parentElement.style.display='none'" style="
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.8rem;
            ">关闭</button>
        </div>
    </div>

    <style>
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @media (max-width: 768px) {
            #navbarUpdateNotice {
                bottom: 10px !important;
                right: 10px !important;
                left: 10px !important;
                max-width: none !important;
            }
        }
    </style>

    <script>
        // 显示导航栏更新提示
        setTimeout(() => {
            const hasShownNotice = localStorage.getItem('navbarNoticeShown');
            if (!hasShownNotice) {
                document.getElementById('navbarUpdateNotice').style.display = 'block';
                localStorage.setItem('navbarNoticeShown', 'true');

                // 10秒后自动隐藏
                setTimeout(() => {
                    const notice = document.getElementById('navbarUpdateNotice');
                    if (notice && notice.style.display !== 'none') {
                        notice.style.display = 'none';
                    }
                }, 10000);
            }
        }, 2000);

        // 全局会话刷新函数
        window.refreshUserSession = function() {
            return fetch('api/refresh-session.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新页面中的用户头像和昵称
                        const userAvatar = document.querySelector('.user-avatar-img');
                        const userNickname = document.querySelector('.user-nickname');

                        if (userAvatar && data.user.avatar) {
                            userAvatar.src = data.user.avatar;
                        }

                        if (userNickname && data.user.nickname) {
                            userNickname.textContent = data.user.nickname;
                        }

                        console.log('用户会话已刷新');
                        return data;
                    } else {
                        console.error('刷新会话失败:', data.message);
                        return data;
                    }
                })
                .catch(error => {
                    console.error('刷新会话请求失败:', error);
                    return { success: false, error: error.message };
                });
        };
    </script>
</body>
</html>