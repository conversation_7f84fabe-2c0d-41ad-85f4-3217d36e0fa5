@echo off
chcp 65001 >nul
echo ================================
echo 比特熊智慧系统版本回退脚本
echo ================================
echo.

set "CURRENT_DIR=%~dp0"
set "BACKUP_PATH=D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1 文件"
set "TEMP_BACKUP=%CURRENT_DIR%temp_current_backup"

echo 正在检查备份文件夹是否存在...
if not exist "%BACKUP_PATH%" (
    echo 错误：备份文件夹不存在！
    echo 路径：%BACKUP_PATH%
    echo.
    echo 请检查以下可能的原因：
    echo 1. 备份文件夹路径是否正确
    echo 2. 备份文件夹是否已被移动或删除
    echo.
    pause
    exit /b 1
)

echo 找到备份文件夹：%BACKUP_PATH%
echo.

echo 正在创建当前版本的临时备份...
if exist "%TEMP_BACKUP%" rmdir /s /q "%TEMP_BACKUP%"
mkdir "%TEMP_BACKUP%"
xcopy "%CURRENT_DIR%*" "%TEMP_BACKUP%\" /e /h /y /exclude:"%~nx0" >nul 2>&1

echo 正在清理当前目录...
for /f "delims=" %%i in ('dir /b /a-d "%CURRENT_DIR%"') do (
    if not "%%i"=="%~nx0" del /q "%CURRENT_DIR%%%i" >nul 2>&1
)

for /f "delims=" %%i in ('dir /b /ad "%CURRENT_DIR%"') do (
    if not "%%i"=="temp_current_backup" rmdir /s /q "%CURRENT_DIR%%%i" >nul 2>&1
)

echo 正在从备份恢复文件...
xcopy "%BACKUP_PATH%\*" "%CURRENT_DIR%" /e /h /y >nul 2>&1

if %errorlevel% neq 0 (
    echo 错误：恢复失败！正在回滚...
    xcopy "%TEMP_BACKUP%\*" "%CURRENT_DIR%" /e /h /y >nul 2>&1
    rmdir /s /q "%TEMP_BACKUP%"
    echo 已回滚到原始状态
    pause
    exit /b 1
)

echo 版本回退成功！
echo.

echo 正在清理临时文件...
rmdir /s /q "%TEMP_BACKUP%"

echo 正在启动项目...
echo.

REM 检查是否存在启动脚本
if exist "启动项目.bat" (
    echo 找到启动脚本：启动项目.bat
    call "启动项目.bat"
) else if exist "start_server.bat" (
    echo 找到启动脚本：start_server.bat
    call "start_server.bat"
) else if exist "快速启动后台.bat" (
    echo 找到启动脚本：快速启动后台.bat
    call "快速启动后台.bat"
) else (
    echo 未找到启动脚本，尝试手动启动...
    
    REM 检查PHP环境
    php --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo PHP环境可用，启动PHP服务器...
        start "PHP服务器" php -S localhost:8000 -t .
        echo PHP服务器已启动在 http://localhost:8000
    ) else (
        echo PHP环境不可用，尝试启动Python服务器...
        python --version >nul 2>&1
        if %errorlevel% equ 0 (
            start "Python服务器" python -m http.server 8000
            echo Python服务器已启动在 http://localhost:8000
        ) else (
            echo 未找到PHP或Python环境，请手动启动项目
        )
    )
)

echo.
echo ================================
echo 版本回退和项目启动完成！
echo ================================
echo.
echo 如果项目启动成功，请在浏览器中访问：
echo http://localhost:8000
echo.
pause
