@echo off
echo ================================
echo BitBear System Version Rollback
echo ================================
echo.

set "CURRENT_DIR=%~dp0"
set "BACKUP_BASE=D:\比特熊组织智慧系统备份文件"

echo Searching for backup folder...

REM Check if backup base directory exists
if not exist "%BACKUP_BASE%" (
    echo Error: Backup base directory not found!
    echo Path: %BACKUP_BASE%
    echo.
    echo Please check if the backup folder exists.
    pause
    exit /b 1
)

echo Found backup base directory.
echo.

REM List available backups
echo Available backups:
dir /b /ad "%BACKUP_BASE%"
echo.

REM Try to find the 6th backup
set "BACKUP_PATH="
for /f "delims=" %%f in ('dir /b /ad "%BACKUP_BASE%\*第6次备份*" 2^>nul') do (
    set "BACKUP_PATH=%BACKUP_BASE%\%%f"
    goto :found_backup
)

REM If 6th backup not found, list all and ask user
if not defined BACKUP_PATH (
    echo 6th backup not found automatically.
    echo Please enter the exact backup folder name:
    set /p "BACKUP_NAME=Backup folder name: "
    set "BACKUP_PATH=%BACKUP_BASE%\%BACKUP_NAME%"
)

:found_backup
if not exist "%BACKUP_PATH%" (
    echo Error: Backup path does not exist!
    echo Path: %BACKUP_PATH%
    pause
    exit /b 1
)

echo Using backup: %BACKUP_PATH%
echo.

echo Creating safety backup of current version...
set "SAFETY_BACKUP=%CURRENT_DIR%current_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "SAFETY_BACKUP=%SAFETY_BACKUP: =0%"
mkdir "%SAFETY_BACKUP%" 2>nul

REM Backup current files (exclude this script)
for %%f in ("%CURRENT_DIR%*") do (
    if not "%%~nxf"=="%~nx0" (
        xcopy "%%f" "%SAFETY_BACKUP%\" /e /h /y >nul 2>&1
    )
)

echo Clearing current directory...
for /f "delims=" %%i in ('dir /b /a-d "%CURRENT_DIR%"') do (
    if not "%%i"=="%~nx0" (
        del /q "%CURRENT_DIR%%%i" >nul 2>&1
    )
)

for /f "delims=" %%i in ('dir /b /ad "%CURRENT_DIR%"') do (
    if not "%%~nxi"=="current_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%" (
        rmdir /s /q "%CURRENT_DIR%%%i" >nul 2>&1
    )
)

echo Restoring from backup...
xcopy "%BACKUP_PATH%\*" "%CURRENT_DIR%" /e /h /y >nul 2>&1

if %errorlevel% neq 0 (
    echo Error: Restore failed! Rolling back...
    xcopy "%SAFETY_BACKUP%\*" "%CURRENT_DIR%" /e /h /y >nul 2>&1
    echo Rollback completed.
    pause
    exit /b 1
)

echo Version rollback successful!
echo.

echo Starting project...
echo.

REM Try different startup methods
if exist "启动项目.bat" (
    echo Starting with 启动项目.bat...
    start "" "启动项目.bat"
) else if exist "start_server.bat" (
    echo Starting with start_server.bat...
    start "" "start_server.bat"
) else if exist "快速启动后台.bat" (
    echo Starting with 快速启动后台.bat...
    start "" "快速启动后台.bat"
) else (
    echo No startup script found. Trying manual startup...
    
    REM Check PHP
    php --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo Starting PHP server...
        start "BitBear PHP Server" cmd /k "php -S localhost:8000 -t . && pause"
    ) else (
        echo PHP not available. Trying Python...
        python --version >nul 2>&1
        if %errorlevel% equ 0 (
            start "BitBear Python Server" cmd /k "python -m http.server 8000 && pause"
        ) else (
            echo Neither PHP nor Python available.
            echo Please install PHP or Python to run the server.
        )
    )
)

REM Wait and open browser
timeout /t 3 >nul
start http://localhost:8000 >nul 2>&1

echo.
echo ================================
echo Rollback and startup complete!
echo ================================
echo.
echo Please check:
echo 1. Browser should open http://localhost:8000
echo 2. If not opened automatically, visit manually
echo 3. Current version backed up to: %SAFETY_BACKUP%
echo.
pause
