@echo off
echo 比特熊智慧系统一键回退工具
echo ========================
echo.

echo 正在执行版本回退...
echo.

echo 1. 创建安全备份...
mkdir "当前版本安全备份" 2>nul
copy * "当前版本安全备份\" >nul 2>&1
xcopy admin "当前版本安全备份\admin\" /e /i /h /y >nul 2>&1
xcopy api "当前版本安全备份\api\" /e /i /h /y >nul 2>&1
xcopy assets "当前版本安全备份\assets\" /e /i /h /y >nul 2>&1
xcopy classes "当前版本安全备份\classes\" /e /i /h /y >nul 2>&1
xcopy components "当前版本安全备份\components\" /e /i /h /y >nul 2>&1
xcopy config "当前版本安全备份\config\" /e /i /h /y >nul 2>&1
xcopy database "当前版本安全备份\database\" /e /i /h /y >nul 2>&1
xcopy image "当前版本安全备份\image\" /e /i /h /y >nul 2>&1
xcopy includes "当前版本安全备份\includes\" /e /i /h /y >nul 2>&1
xcopy uploads "当前版本安全备份\uploads\" /e /i /h /y >nul 2>&1
echo 安全备份完成！

echo.
echo 2. 清理当前文件...
del *.php >nul 2>&1
del *.html >nul 2>&1
del *.css >nul 2>&1
del *.js >nul 2>&1
del *.md >nul 2>&1
del *.txt >nul 2>&1
del *.sh >nul 2>&1
del *.exp >nul 2>&1
del *.py >nul 2>&1
rmdir /s /q admin >nul 2>&1
rmdir /s /q api >nul 2>&1
rmdir /s /q assets >nul 2>&1
rmdir /s /q classes >nul 2>&1
rmdir /s /q components >nul 2>&1
rmdir /s /q config >nul 2>&1
rmdir /s /q database >nul 2>&1
rmdir /s /q image >nul 2>&1
rmdir /s /q includes >nul 2>&1
rmdir /s /q uploads >nul 2>&1
echo 文件清理完成！

echo.
echo 3. 从备份恢复文件...
xcopy "D:\比特熊组织智慧系统备份文件\比特熊智慧系统(v0.0.1) (第6次备份) - T1\*" . /e /i /h /y
if %errorlevel% neq 0 (
    echo 恢复失败！正在回滚...
    xcopy "当前版本安全备份\*" . /e /i /h /y >nul 2>&1
    echo 已回滚到原始状态
    pause
    exit /b 1
)
echo 文件恢复完成！

echo.
echo 4. 启动项目...
if exist "启动项目.bat" (
    echo 找到启动脚本，正在启动...
    start "" "启动项目.bat"
) else if exist "index-standalone.html" (
    echo 打开静态版本...
    start "" "index-standalone.html"
) else (
    echo 请手动启动项目
)

echo.
echo ========================
echo 版本回退完成！
echo ========================
echo.
echo 安全备份保存在：当前版本安全备份
echo.
pause
