<?php
/**
 * 管理后台社区管理API
 * 处理帖子、评论、用户、分类的管理操作
 */

session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/Auth.php';

header('Content-Type: application/json');

// 权限检查 - 支持管理后台和普通用户两种登录方式
$isAdminLoggedIn = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$isUserLoggedIn = isset($_SESSION['user_id']) && isset($_SESSION['role']) && in_array($_SESSION['role'], ['admin', 'super_admin']);

if (!$isAdminLoggedIn && !$isUserLoggedIn) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未登录或权限不足']);
    exit;
}

$db = db();
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGet($db, $action);
            break;
        case 'POST':
            handlePost($db);
            break;
        case 'PUT':
            handlePut($db);
            break;
        case 'DELETE':
            handleDelete($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}

function handleGet($db, $action) {
    switch ($action) {
        case 'posts':
            getPosts($db);
            break;
        case 'comments':
            getComments($db);
            break;
        case 'users':
            getCommunityUsers($db);
            break;
        case 'user_details':
            getUserDetails($db);
            break;
        case 'categories':
            getCategories($db);
            break;
        case 'stats':
            getCommunityStats($db);
            break;
        case 'export_posts':
            exportPosts($db);
            break;
        case 'comment_detail':
            getCommentDetail($db);
            break;
        case 'get_comment':
            getSingleComment($db);
            break;
        case 'export_comments':
            exportComments($db);
            break;
        case 'users':
            getUsers($db);
            break;
        case 'user_activity':
            getUserActivity($db);
            break;
        case 'export_users':
            exportUsers($db);
            break;
        case 'get_notifications':
            getNotifications($db);
            break;
        case 'categories':
            getCategories($db);
            break;
        case 'category_detail':
            getCategoryDetail($db);
            break;
        case 'overview_stats':
            getOverviewStats($db);
            break;
        case 'posts_trend':
            getPostsTrend($db);
            break;
        case 'users_activity':
            getUsersActivity($db);
            break;
        case 'category_stats':
            getCategoryStats($db);
            break;
        case 'popular_posts':
            getPopularPosts($db);
            break;
        case 'active_users':
            getActiveUsers($db);
            break;
        case 'system_status':
            getSystemStatus($db);
            break;
        case 'export_statistics':
            exportStatistics($db);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
}

function getPosts($db) {
    try {
        $filter = $_GET['filter'] ?? 'all';
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = 20;
        $offset = ($page - 1) * $limit;
        $search = $_GET['search'] ?? '';

    // 构建WHERE条件
    $whereConditions = [];
    $params = [];

    if ($filter !== 'all') {
        $whereConditions[] = "p.status = ?";
        $params[] = $filter;
    }

    if (!empty($search)) {
        $whereConditions[] = "(p.title LIKE ? OR p.content LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

    // 获取帖子列表 - 修复表名
    $sql = "SELECT p.*, u.username, up.nickname,
                   pc.name as category_name, pc.color as category_color,
                   (SELECT COUNT(*) FROM comments cm WHERE cm.post_id = p.id AND cm.status != 'deleted') as comments_count
            FROM posts p
            LEFT JOIN users u ON p.user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN post_categories pc ON p.category_id = pc.id
            $whereClause
            ORDER BY p.is_pinned DESC, p.created_at DESC
            LIMIT ? OFFSET ?";

    $params[] = $limit;
    $params[] = $offset;

    $posts = $db->fetchAll($sql, $params);

    // 处理作者名称
    foreach ($posts as &$post) {
        $post['author_name'] = $post['nickname'] ?? $post['username'] ?? '未知用户';
        // 确保必要字段存在
        $post['status'] = $post['status'] ?? 'published';
        $post['is_pinned'] = $post['is_pinned'] ?? 0;
        $post['comments_count'] = $post['comments_count'] ?? 0;
    }

    // 获取总数用于分页
    $countSql = "SELECT COUNT(*) as total FROM posts p $whereClause";
    $countParams = array_slice($params, 0, -2); // 移除limit和offset参数
    $totalResult = $db->fetchOne($countSql, $countParams);
    $total = $totalResult['total'];

    $pagination = [
        'current_page' => $page,
        'total_pages' => ceil($total / $limit),
        'total_items' => $total,
        'per_page' => $limit
    ];

        echo json_encode([
            'success' => true,
            'posts' => $posts,
            'pagination' => $pagination
        ]);
    } catch (Exception $e) {
        error_log("获取帖子列表失败: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => '获取帖子列表失败: ' . $e->getMessage(),
            'debug' => [
                'filter' => $filter ?? 'unknown',
                'page' => $page ?? 'unknown',
                'search' => $search ?? 'unknown'
            ]
        ]);
    }
}

function getComments($db) {
    $filter = $_GET['filter'] ?? 'all';
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = 20;
    $offset = ($page - 1) * $limit;
    $search = $_GET['search'] ?? '';

    // 构建WHERE条件
    $whereConditions = ["c.status != 'deleted'"];
    $params = [];

    if ($filter !== 'all') {
        $whereConditions[] = "c.status = ?";
        $params[] = $filter;
    }

    if (!empty($search)) {
        $whereConditions[] = "c.content LIKE ?";
        $params[] = "%$search%";
    }

    $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

    // 获取评论列表
    $sql = "SELECT c.*, u.username, u.full_name, up.nickname, up.avatar_url,
                   p.title as post_title,
                   (SELECT COUNT(*) FROM likes l WHERE l.target_type = 'comment' AND l.target_id = c.id) as likes_count,
                   (SELECT COUNT(*) FROM reports r WHERE r.target_type = 'comment' AND r.target_id = c.id) as reports_count
            FROM comments c
            LEFT JOIN users u ON c.user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN posts p ON c.post_id = p.id
            $whereClause
            ORDER BY c.created_at DESC
            LIMIT ? OFFSET ?";

    $params[] = $limit;
    $params[] = $offset;

    $comments = $db->fetchAll($sql, $params);

    // 处理作者名称
    foreach ($comments as &$comment) {
        $comment['author_name'] = $comment['nickname'] ?? $comment['full_name'] ?? $comment['username'];
    }

    // 获取总数用于分页
    $countSql = "SELECT COUNT(*) as total FROM comments c $whereClause";
    $countParams = array_slice($params, 0, -2);
    $totalResult = $db->fetchOne($countSql, $countParams);
    $total = $totalResult['total'];

    $pagination = [
        'current_page' => $page,
        'total_pages' => ceil($total / $limit),
        'total_items' => $total,
        'per_page' => $limit
    ];

    echo json_encode([
        'success' => true,
        'comments' => $comments,
        'pagination' => $pagination
    ]);
}

function getCommunityUsers($db) {
    $filter = $_GET['filter'] ?? 'all';
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = 20;
    $offset = ($page - 1) * $limit;
    $search = $_GET['search'] ?? '';

    // 构建WHERE条件
    $whereConditions = [];
    $params = [];

    if ($filter === 'active') {
        $whereConditions[] = "u.last_login_at > DATE_SUB(NOW(), INTERVAL 30 DAY)";
    } elseif ($filter === 'banned') {
        $whereConditions[] = "up.is_banned = 1";
    }

    if (!empty($search)) {
        $whereConditions[] = "(u.username LIKE ? OR u.full_name LIKE ? OR up.nickname LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

    // 获取用户列表
    $sql = "SELECT u.id, u.username, u.email, u.role, u.status, u.created_at, u.last_login_at,
                   u.ban_reason, u.ban_until,
                   up.nickname, up.full_name, up.avatar_url, up.signature, up.is_banned,
                   (SELECT COUNT(*) FROM posts p WHERE p.user_id = u.id) as posts_count,
                   (SELECT COUNT(*) FROM comments c WHERE c.user_id = u.id AND c.status != 'deleted') as comments_count
            FROM users u
            LEFT JOIN user_profiles up ON u.id = up.user_id
            $whereClause
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?";

    $params[] = $limit;
    $params[] = $offset;

    $users = $db->fetchAll($sql, $params);

    // 获取总数用于分页
    $countSql = "SELECT COUNT(*) as total FROM users u LEFT JOIN user_profiles up ON u.id = up.user_id $whereClause";
    $countParams = array_slice($params, 0, -2);
    $totalResult = $db->fetchOne($countSql, $countParams);
    $total = $totalResult['total'];

    $pagination = [
        'current_page' => $page,
        'total_pages' => ceil($total / $limit),
        'total_items' => $total,
        'per_page' => $limit
    ];

    echo json_encode([
        'success' => true,
        'users' => $users,
        'pagination' => $pagination
    ]);
}

function getUserDetails($db) {
    $userId = (int)($_GET['id'] ?? 0);
    if (!$userId) {
        echo json_encode(['success' => false, 'error' => '缺少用户ID']);
        return;
    }

    // 获取用户详细信息
    $sql = "SELECT u.id, u.username, u.email, u.full_name, u.status, u.created_at, u.last_login, u.login_count,
                   up.nickname, up.avatar_url, up.bio, up.signature, up.is_banned,
                   r.role_name, r.role_code,
                   (SELECT COUNT(*) FROM posts p WHERE p.user_id = u.id) as posts_count,
                   (SELECT COUNT(*) FROM comments c WHERE c.user_id = u.id AND c.status != 'deleted') as comments_count
            FROM users u
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN user_roles r ON u.role_id = r.id
            WHERE u.id = ?";

    $user = $db->fetchOne($sql, [$userId]);

    if (!$user) {
        echo json_encode(['success' => false, 'error' => '用户不存在']);
        return;
    }

    // 处理用户数据
    $user['is_banned'] = (bool)($user['is_banned'] ?? false);

    echo json_encode([
        'success' => true,
        'user' => $user
    ]);
}

function getCategories($db) {
    $sql = "SELECT pc.*, COUNT(p.id) as posts_count
            FROM post_categories pc
            LEFT JOIN posts p ON pc.id = p.category_id
            GROUP BY pc.id
            ORDER BY pc.sort_order ASC, pc.id ASC";

    $categories = $db->fetchAll($sql);

    echo json_encode(['success' => true, 'categories' => $categories]);
}

function getCommunityStats($db) {
    // 获取各种统计数据
    $stats = [];

    // 总帖子数
    $result = $db->fetchOne("SELECT COUNT(*) as count FROM posts WHERE status = 'published'");
    $stats['total_posts'] = $result['count'];

    // 待审核帖子数
    $result = $db->fetchOne("SELECT COUNT(*) as count FROM posts WHERE status = 'pending'");
    $stats['pending_posts'] = $result['count'];

    // 总评论数
    $result = $db->fetchOne("SELECT COUNT(*) as count FROM comments WHERE status != 'deleted'");
    $stats['total_comments'] = $result['count'];

    // 待审核评论数
    $result = $db->fetchOne("SELECT COUNT(*) as count FROM comments WHERE status = 'pending'");
    $stats['pending_comments'] = $result['count'];

    // 活跃用户数（30天内登录）
    $result = $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE last_login_at > DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $stats['active_users'] = $result['count'];

    // 今日新增帖子
    $result = $db->fetchOne("SELECT COUNT(*) as count FROM posts WHERE DATE(created_at) = CURDATE()");
    $stats['today_posts'] = $result['count'];

    echo json_encode([
        'success' => true,
        'stats' => $stats
    ]);
}

function handlePost($db) {
    // 支持两种输入格式：JSON和表单数据
    $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
    if (strpos($contentType, 'application/json') !== false) {
        $input = json_decode(file_get_contents('php://input'), true);
    } else {
        $input = $_POST;
    }
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'create_post':
            createPost($db, $input);
            break;
        case 'add_category':
            addCategory($db, $input);
            break;
        case 'update_user':
            updateUserInfo($db, $input);
            break;
        case 'ban_user':
            banUser($db, $input);
            break;
        case 'send_notification':
            sendUserNotification($db, $input);
            break;
        case 'delete_user':
            deleteUserAccount($db, $input);
            break;
        case 'mark_notification_read':
            markNotificationAsRead($db, $input);
            break;
        case 'update_comment':
            updateComment($db, $input);
            break;
        case 'delete_comment':
            deleteComment($db, $input);
            break;
        case 'update_comment_status':
            updateCommentStatus($db, $input);
            break;
        case 'dismiss_comment_reports':
            dismissCommentReports($db, $input);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
}

function handlePut($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'pin_post':
            pinPost($db, $input);
            break;
        case 'approve_comment':
            approveComment($db, $input);
            break;
        case 'ban_user':
            banUser($db, $input);
            break;
        case 'update_user':
            updateUser($db, $input);
            break;
        case 'update_post_status':
            updatePostStatus($db, $input);
            break;
        case 'reject_comment':
            rejectComment($db, $input);
            break;
        case 'ban_user':
            banUser($db, $input);
            break;
        case 'unban_user':
            unbanUser($db, $input);
            break;
        case 'update_category':
            updateCategory($db, $input);
            break;
        case 'bulk_update_posts':
            bulkUpdatePosts($db, $input);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
}

function handleDelete($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'delete_post':
            deletePost($db, $input);
            break;
        case 'delete_comment':
            deleteComment($db, $input);
            break;
        case 'delete_user':
            deleteUser($db, $input);
            break;
        case 'delete_category':
            deleteCategory($db, $input);
            break;
        case 'bulk_delete_posts':
            bulkDeletePosts($db, $input);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
}

function pinPost($db, $input) {
    $postId = intval($input['id']);
    $isPinned = $input['is_pinned'] === 'true' ? 1 : 0;

    $sql = "UPDATE posts SET is_pinned = ? WHERE id = ?";
    $result = $db->execute($sql, [$isPinned, $postId]);

    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => '操作失败']);
    }
}

function deletePost($db, $input) {
    $postId = intval($input['id']);

    // 软删除帖子
    $sql = "UPDATE posts SET status = 'deleted' WHERE id = ?";
    $result = $db->execute($sql, [$postId]);

    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => '删除失败']);
    }
}

function deleteComment($db, $input) {
    $commentId = intval($input['id']);

    // 软删除评论
    $sql = "UPDATE comments SET status = 'deleted' WHERE id = ?";
    $result = $db->execute($sql, [$commentId]);

    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => '删除失败']);
    }
}

function approveComment($db, $input) {
    $commentId = intval($input['id']);

    $sql = "UPDATE comments SET status = 'approved' WHERE id = ?";
    $result = $db->execute($sql, [$commentId]);

    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => '操作失败']);
    }
}

function banUser($db, $input) {
    $userId = intval($input['user_id'] ?? $input['id'] ?? 0);
    $duration = $input['duration'] ?? '';
    $reason = trim($input['reason'] ?? '违反社区规定');

    if ($userId <= 0) {
        echo json_encode(['success' => false, 'error' => '无效的用户ID']);
        return;
    }

    // 计算封禁结束时间
    $banUntil = null;
    if (!empty($duration) && $duration !== 'permanent') {
        $days = intval($duration);
        $banUntil = date('Y-m-d H:i:s', strtotime("+{$days} days"));
    }

    try {
        $db->beginTransaction();

        // 更新用户状态
        $sql = "UPDATE users SET status = 'banned', ban_reason = ?, ban_until = ? WHERE id = ?";
        $db->execute($sql, [$reason, $banUntil, $userId]);

        // 记录封禁日志（如果表存在）
        try {
            $logSql = "INSERT INTO user_ban_logs (user_id, reason, duration, banned_at, banned_until) VALUES (?, ?, ?, NOW(), ?)";
            $db->execute($logSql, [$userId, $reason, $duration, $banUntil]);
        } catch (Exception $e) {
            // 如果表不存在，忽略错误
        }

        $db->commit();
        echo json_encode(['success' => true]);
    } catch (Exception $e) {
        $db->rollback();
        echo json_encode(['success' => false, 'error' => '封禁失败: ' . $e->getMessage()]);
    }
}

function addCategory($db, $input) {
    $name = trim($input['name'] ?? '');
    $description = trim($input['description'] ?? '');
    $color = trim($input['color'] ?? '#3b82f6');
    $icon = trim($input['icon'] ?? '');
    $sortOrder = intval($input['sort_order'] ?? 0);

    if (empty($name)) {
        echo json_encode(['success' => false, 'error' => '分类名称不能为空']);
        return;
    }

    // 生成slug（URL友好的标识符）
    $slug = generateSlug($name);

    // 检查slug是否已存在
    $existingSlug = $db->fetchOne("SELECT id FROM post_categories WHERE slug = ?", [$slug]);
    if ($existingSlug) {
        $slug = $slug . '_' . time(); // 如果存在，添加时间戳
    }

    $sql = "INSERT INTO post_categories (name, slug, description, color, icon, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, 1)";
    $result = $db->execute($sql, [$name, $slug, $description, $color, $icon, $sortOrder]);

    if ($result) {
        echo json_encode(['success' => true, 'id' => $db->lastInsertId(), 'slug' => $slug]);
    } else {
        echo json_encode(['success' => false, 'error' => '添加失败']);
    }
}

// 生成URL友好的slug
function generateSlug($text) {
    // 移除特殊字符，转换为小写
    $slug = strtolower(trim($text));

    // 中文字符转换为拼音或使用简化处理
    $slug = preg_replace('/[^\w\s-]/', '', $slug);
    $slug = preg_replace('/[\s_-]+/', '-', $slug);
    $slug = trim($slug, '-');

    // 如果slug为空（比如全是中文），使用时间戳
    if (empty($slug)) {
        $slug = 'category_' . time();
    }

    return $slug;
}

function updateUser($db, $input) {
    $userId = intval($input['user_id'] ?? 0);
    if (!$userId) {
        echo json_encode(['success' => false, 'error' => '缺少用户ID']);
        return;
    }

    try {
        // 开始事务
        $db->execute("BEGIN");

        // 更新users表
        $userFields = [];
        $userParams = [];

        if (isset($input['username']) && !empty($input['username'])) {
            $userFields[] = "username = ?";
            $userParams[] = $input['username'];
        }

        if (isset($input['email']) && !empty($input['email'])) {
            $userFields[] = "email = ?";
            $userParams[] = $input['email'];
        }

        if (isset($input['full_name'])) {
            $userFields[] = "full_name = ?";
            $userParams[] = $input['full_name'];
        }

        if (isset($input['status']) && !empty($input['status'])) {
            $userFields[] = "status = ?";
            $userParams[] = $input['status'];
        }

        if (!empty($userFields)) {
            $userParams[] = $userId;
            $userSql = "UPDATE users SET " . implode(', ', $userFields) . " WHERE id = ?";
            $db->execute($userSql, $userParams);
        }

        // 更新user_profiles表
        $profileFields = [];
        $profileParams = [];

        if (isset($input['nickname'])) {
            $profileFields[] = "nickname = ?";
            $profileParams[] = $input['nickname'];
        }

        if (isset($input['bio'])) {
            $profileFields[] = "bio = ?";
            $profileParams[] = $input['bio'];
        }

        if (!empty($profileFields)) {
            $profileParams[] = $userId;
            $profileSql = "UPDATE user_profiles SET " . implode(', ', $profileFields) . " WHERE user_id = ?";
            $db->execute($profileSql, $profileParams);
        }

        // 提交事务
        $db->execute("COMMIT");

        echo json_encode(['success' => true, 'message' => '用户信息更新成功']);

    } catch (Exception $e) {
        // 回滚事务
        $db->execute("ROLLBACK");
        error_log("更新用户信息失败: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => '更新失败: ' . $e->getMessage()]);
    }
}

function updatePostStatus($db, $input) {
    $postId = intval($input['id']);
    $status = $input['status'] ?? '';

    $allowedStatuses = ['published', 'draft', 'hidden', 'deleted'];
    if (!in_array($status, $allowedStatuses)) {
        echo json_encode(['success' => false, 'error' => '无效的状态']);
        return;
    }

    $sql = "UPDATE posts SET status = ? WHERE id = ?";
    $result = $db->execute($sql, [$status, $postId]);

    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => '更新失败']);
    }
}

function exportPosts($db) {
    $filter = $_GET['filter'] ?? 'all';
    $search = $_GET['search'] ?? '';

    // 构建WHERE条件
    $whereConditions = [];
    $params = [];

    if ($filter !== 'all') {
        $whereConditions[] = "p.status = ?";
        $params[] = $filter;
    }

    if (!empty($search)) {
        $whereConditions[] = "(p.title LIKE ? OR p.content LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

    // 获取帖子数据
    $sql = "SELECT p.id, p.title, p.content, p.status, p.view_count, p.like_count, p.comment_count,
                   p.created_at, p.updated_at, u.username, u.full_name, up.nickname,
                   pc.name as category_name
            FROM posts p
            LEFT JOIN users u ON p.user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN post_categories pc ON p.category_id = pc.id
            $whereClause
            ORDER BY p.created_at DESC";

    $posts = $db->fetchAll($sql, $params);

    // 设置CSV下载头
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="posts_export_' . date('Y-m-d_H-i-s') . '.csv"');

    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";

    // 输出CSV头
    $headers = ['ID', '标题', '作者', '分类', '状态', '浏览量', '点赞数', '评论数', '创建时间', '更新时间'];
    echo implode(',', array_map(function($header) {
        return '"' . str_replace('"', '""', $header) . '"';
    }, $headers)) . "\n";

    // 输出数据
    foreach ($posts as $post) {
        $authorName = $post['nickname'] ?? $post['full_name'] ?? $post['username'];
        $statusText = [
            'published' => '已发布',
            'draft' => '草稿',
            'pending' => '待审核',
            'rejected' => '已拒绝'
        ][$post['status']] ?? $post['status'];

        $row = [
            $post['id'],
            $post['title'],
            $authorName,
            $post['category_name'] ?? '无分类',
            $statusText,
            $post['view_count'] ?? 0,
            $post['like_count'] ?? 0,
            $post['comment_count'] ?? 0,
            $post['created_at'],
            $post['updated_at']
        ];

        echo implode(',', array_map(function($field) {
            return '"' . str_replace('"', '""', $field) . '"';
        }, $row)) . "\n";
    }

    exit;
}

function getCommentDetail($db) {
    $commentId = intval($_GET['id']);

    $sql = "SELECT c.*, u.username, u.full_name, up.nickname, up.avatar_url,
                   p.title as post_title
            FROM comments c
            LEFT JOIN users u ON c.user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN posts p ON c.post_id = p.id
            WHERE c.id = ?";

    $comment = $db->fetchOne($sql, [$commentId]);

    if ($comment) {
        $comment['author_name'] = $comment['nickname'] ?? $comment['full_name'] ?? $comment['username'];
        echo json_encode(['success' => true, 'comment' => $comment]);
    } else {
        echo json_encode(['success' => false, 'error' => '评论不存在']);
    }
}

function rejectComment($db, $input) {
    $commentId = intval($input['id']);

    $sql = "UPDATE comments SET status = 'rejected' WHERE id = ?";
    $result = $db->execute($sql, [$commentId]);

    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => '操作失败']);
    }
}

function exportComments($db) {
    $filter = $_GET['filter'] ?? 'all';
    $search = $_GET['search'] ?? '';

    // 构建WHERE条件
    $whereConditions = [];
    $params = [];

    if ($filter !== 'all') {
        $whereConditions[] = "c.status = ?";
        $params[] = $filter;
    }

    if (!empty($search)) {
        $whereConditions[] = "(c.content LIKE ? OR u.username LIKE ? OR up.nickname LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

    // 获取评论数据
    $sql = "SELECT c.id, c.content, c.status, c.like_count, c.created_at,
                   u.username, u.full_name, up.nickname,
                   p.title as post_title
            FROM comments c
            LEFT JOIN users u ON c.user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN posts p ON c.post_id = p.id
            $whereClause
            ORDER BY c.created_at DESC";

    $comments = $db->fetchAll($sql, $params);

    // 设置CSV下载头
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="comments_export_' . date('Y-m-d_H-i-s') . '.csv"');

    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";

    // 输出CSV头
    $headers = ['ID', '内容', '作者', '所属帖子', '状态', '点赞数', '创建时间'];
    echo implode(',', array_map(function($header) {
        return '"' . str_replace('"', '""', $header) . '"';
    }, $headers)) . "\n";

    // 输出数据
    foreach ($comments as $comment) {
        $authorName = $comment['nickname'] ?? $comment['full_name'] ?? $comment['username'];
        $statusText = [
            'approved' => '已通过',
            'pending' => '待审核',
            'rejected' => '已拒绝',
            'reported' => '被举报'
        ][$comment['status']] ?? $comment['status'];

        $row = [
            $comment['id'],
            $comment['content'],
            $authorName,
            $comment['post_title'] ?? '未知帖子',
            $statusText,
            $comment['like_count'] ?? 0,
            $comment['created_at']
        ];

        echo implode(',', array_map(function($field) {
            return '"' . str_replace('"', '""', $field) . '"';
        }, $row)) . "\n";
    }

    exit;
}

function getUsers($db) {
    $filter = $_GET['filter'] ?? 'all';
    $page = intval($_GET['page'] ?? 1);
    $search = $_GET['search'] ?? '';
    $limit = 20;
    $offset = ($page - 1) * $limit;

    // 构建WHERE条件
    $whereConditions = [];
    $params = [];

    if ($filter !== 'all') {
        $whereConditions[] = "u.status = ?";
        $params[] = $filter;
    }

    if (!empty($search)) {
        $whereConditions[] = "(u.username LIKE ? OR u.email LIKE ? OR up.nickname LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

    // 获取总数
    $countSql = "SELECT COUNT(*) as total
                 FROM users u
                 LEFT JOIN user_profiles up ON u.id = up.user_id
                 $whereClause";
    $totalResult = $db->fetchOne($countSql, $params);
    $total = $totalResult['total'];

    // 获取用户数据
    $sql = "SELECT u.id, u.username, u.email, u.full_name, u.status, u.created_at,
                   up.nickname, up.avatar_url,
                   COUNT(DISTINCT p.id) as posts_count,
                   COUNT(DISTINCT c.id) as comments_count
            FROM users u
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN posts p ON u.id = p.user_id
            LEFT JOIN comments c ON u.id = c.user_id
            $whereClause
            GROUP BY u.id, u.username, u.email, u.full_name, u.status, u.created_at, up.nickname, up.avatar_url
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?";

    $allParams = array_merge($params, [$limit, $offset]);
    $users = $db->fetchAll($sql, $allParams);

    // 分页信息
    $totalPages = ceil($total / $limit);
    $pagination = [
        'current_page' => $page,
        'total_pages' => $totalPages,
        'total_items' => $total,
        'per_page' => $limit
    ];

    echo json_encode([
        'success' => true,
        'users' => $users,
        'pagination' => $pagination
    ]);
}

function getUserActivity($db) {
    $userId = intval($_GET['id']);

    // 获取用户基本统计
    $statsSql = "SELECT
                    (SELECT COUNT(*) FROM posts WHERE user_id = ?) as posts_count,
                    (SELECT COUNT(*) FROM comments WHERE user_id = ?) as comments_count,
                    (SELECT SUM(like_count) FROM posts WHERE user_id = ?) as likes_received,
                    (SELECT MAX(created_at) FROM (
                        SELECT created_at FROM posts WHERE user_id = ?
                        UNION ALL
                        SELECT created_at FROM comments WHERE user_id = ?
                    ) as activities) as last_activity";

    $stats = $db->fetchOne($statsSql, [$userId, $userId, $userId, $userId, $userId]);

    // 获取最近的帖子
    $recentPostsSql = "SELECT id, title, created_at FROM posts WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
    $recentPosts = $db->fetchAll($recentPostsSql, [$userId]);

    // 获取最近的评论
    $recentCommentsSql = "SELECT c.id, c.created_at, p.id as post_id, p.title as post_title
                          FROM comments c
                          LEFT JOIN posts p ON c.post_id = p.id
                          WHERE c.user_id = ?
                          ORDER BY c.created_at DESC LIMIT 5";
    $recentComments = $db->fetchAll($recentCommentsSql, [$userId]);

    $activity = [
        'posts_count' => $stats['posts_count'] ?? 0,
        'comments_count' => $stats['comments_count'] ?? 0,
        'likes_received' => $stats['likes_received'] ?? 0,
        'last_activity' => $stats['last_activity'],
        'recent_posts' => $recentPosts,
        'recent_comments' => $recentComments
    ];

    echo json_encode(['success' => true, 'activity' => $activity]);
}

function unbanUser($db, $input) {
    $userId = intval($input['id']);

    $sql = "UPDATE users SET status = 'active' WHERE id = ?";
    $result = $db->execute($sql, [$userId]);

    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => '操作失败']);
    }
}

function deleteUser($db, $input) {
    $userId = intval($input['id']);

    try {
        // 开始事务
        $db->beginTransaction();

        // 删除用户的评论
        $db->execute("DELETE FROM comments WHERE user_id = ?", [$userId]);

        // 删除用户的帖子
        $db->execute("DELETE FROM posts WHERE user_id = ?", [$userId]);

        // 删除用户资料
        $db->execute("DELETE FROM user_profiles WHERE user_id = ?", [$userId]);

        // 删除用户
        $db->execute("DELETE FROM users WHERE id = ?", [$userId]);

        // 提交事务
        $db->commit();

        echo json_encode(['success' => true]);
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        echo json_encode(['success' => false, 'error' => '删除失败: ' . $e->getMessage()]);
    }
}

function exportUsers($db) {
    $filter = $_GET['filter'] ?? 'all';
    $search = $_GET['search'] ?? '';

    // 构建WHERE条件
    $whereConditions = [];
    $params = [];

    if ($filter !== 'all') {
        $whereConditions[] = "u.status = ?";
        $params[] = $filter;
    }

    if (!empty($search)) {
        $whereConditions[] = "(u.username LIKE ? OR u.email LIKE ? OR up.nickname LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

    // 获取用户数据
    $sql = "SELECT u.id, u.username, u.email, u.full_name, u.status, u.created_at,
                   up.nickname,
                   COUNT(DISTINCT p.id) as posts_count,
                   COUNT(DISTINCT c.id) as comments_count
            FROM users u
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN posts p ON u.id = p.user_id
            LEFT JOIN comments c ON u.id = c.user_id
            $whereClause
            GROUP BY u.id, u.username, u.email, u.full_name, u.status, u.created_at, up.nickname
            ORDER BY u.created_at DESC";

    $users = $db->fetchAll($sql, $params);

    // 设置CSV下载头
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="users_export_' . date('Y-m-d_H-i-s') . '.csv"');

    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";

    // 输出CSV头
    $headers = ['ID', '用户名', '昵称', '邮箱', '状态', '帖子数', '评论数', '注册时间'];
    echo implode(',', array_map(function($header) {
        return '"' . str_replace('"', '""', $header) . '"';
    }, $headers)) . "\n";

    // 输出数据
    foreach ($users as $user) {
        $statusText = [
            'active' => '正常',
            'banned' => '已封禁',
            'inactive' => '未激活',
            'reported' => '被举报'
        ][$user['status']] ?? $user['status'];

        $row = [
            $user['id'],
            $user['username'],
            $user['nickname'] ?? $user['full_name'] ?? '',
            $user['email'] ?? '',
            $statusText,
            $user['posts_count'] ?? 0,
            $user['comments_count'] ?? 0,
            $user['created_at']
        ];

        echo implode(',', array_map(function($field) {
            return '"' . str_replace('"', '""', $field) . '"';
        }, $row)) . "\n";
    }

    exit;
}

function getCategoryDetail($db) {
    $categoryId = intval($_GET['id']);

    $sql = "SELECT * FROM categories WHERE id = ?";
    $category = $db->fetchOne($sql, [$categoryId]);

    if ($category) {
        echo json_encode(['success' => true, 'category' => $category]);
    } else {
        echo json_encode(['success' => false, 'error' => '分类不存在']);
    }
}

function updateCategory($db, $input) {
    $categoryId = intval($input['id']);
    $name = trim($input['name'] ?? '');
    $description = trim($input['description'] ?? '');
    $color = trim($input['color'] ?? '#3b82f6');
    $icon = trim($input['icon'] ?? '');
    $sortOrder = intval($input['sort_order'] ?? 0);
    $isActive = $input['is_active'] ?? false;

    if (empty($name)) {
        echo json_encode(['success' => false, 'error' => '分类名称不能为空']);
        return;
    }

    // 检查名称是否重复（排除当前分类）
    $checkSql = "SELECT id FROM categories WHERE name = ? AND id != ?";
    $existing = $db->fetchOne($checkSql, [$name, $categoryId]);
    if ($existing) {
        echo json_encode(['success' => false, 'error' => '分类名称已存在']);
        return;
    }

    $sql = "UPDATE categories SET name = ?, description = ?, color = ?, icon = ?, sort_order = ?, is_active = ? WHERE id = ?";
    $result = $db->execute($sql, [$name, $description, $color, $icon, $sortOrder, $isActive ? 1 : 0, $categoryId]);

    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => '更新失败']);
    }
}

function deleteCategory($db, $input) {
    $categoryId = intval($input['id']);

    try {
        // 开始事务
        $db->beginTransaction();

        // 将该分类下的帖子设为未分类（category_id = NULL）
        $db->execute("UPDATE posts SET category_id = NULL WHERE category_id = ?", [$categoryId]);

        // 删除分类
        $db->execute("DELETE FROM categories WHERE id = ?", [$categoryId]);

        // 提交事务
        $db->commit();

        echo json_encode(['success' => true]);
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        echo json_encode(['success' => false, 'error' => '删除失败: ' . $e->getMessage()]);
    }
}

function getOverviewStats($db) {
    $stats = [];

    // 总帖子数
    $stats['total_posts'] = $db->fetchOne("SELECT COUNT(*) as count FROM posts")['count'] ?? 0;

    // 总评论数
    $stats['total_comments'] = $db->fetchOne("SELECT COUNT(*) as count FROM comments")['count'] ?? 0;

    // 总用户数
    $stats['total_users'] = $db->fetchOne("SELECT COUNT(*) as count FROM users")['count'] ?? 0;

    // 总浏览量（模拟数据）
    $stats['total_views'] = $stats['total_posts'] * 15 + $stats['total_comments'] * 3;

    // 计算变化百分比（与上月对比）
    $lastMonth = date('Y-m-d', strtotime('-1 month'));

    $lastMonthPosts = $db->fetchOne("SELECT COUNT(*) as count FROM posts WHERE created_at < ?", [$lastMonth])['count'] ?? 0;
    $stats['posts_change'] = $lastMonthPosts > 0 ? round((($stats['total_posts'] - $lastMonthPosts) / $lastMonthPosts) * 100, 1) : 0;

    $lastMonthComments = $db->fetchOne("SELECT COUNT(*) as count FROM comments WHERE created_at < ?", [$lastMonth])['count'] ?? 0;
    $stats['comments_change'] = $lastMonthComments > 0 ? round((($stats['total_comments'] - $lastMonthComments) / $lastMonthComments) * 100, 1) : 0;

    $lastMonthUsers = $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE created_at < ?", [$lastMonth])['count'] ?? 0;
    $stats['users_change'] = $lastMonthUsers > 0 ? round((($stats['total_users'] - $lastMonthUsers) / $lastMonthUsers) * 100, 1) : 0;

    $stats['views_change'] = round(($stats['posts_change'] + $stats['comments_change']) / 2, 1);

    echo json_encode(['success' => true, 'stats' => $stats]);
}

function getPostsTrend($db) {
    $period = intval($_GET['period'] ?? 30);
    $data = [];

    for ($i = $period - 1; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-{$i} days"));
        $count = $db->fetchOne("SELECT COUNT(*) as count FROM posts WHERE DATE(created_at) = ?", [$date])['count'] ?? 0;
        $data[] = [
            'date' => $date,
            'posts' => intval($count)
        ];
    }

    echo json_encode(['success' => true, 'data' => $data]);
}

function getUsersActivity($db) {
    $period = intval($_GET['period'] ?? 30);
    $data = [];

    for ($i = $period - 1; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-{$i} days"));

        // 活跃用户（当天有发帖或评论的用户）
        $activeUsers = $db->fetchOne("
            SELECT COUNT(DISTINCT user_id) as count FROM (
                SELECT user_id FROM posts WHERE DATE(created_at) = ?
                UNION
                SELECT user_id FROM comments WHERE DATE(created_at) = ?
            ) as active_users
        ", [$date, $date])['count'] ?? 0;

        // 新注册用户
        $newUsers = $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = ?", [$date])['count'] ?? 0;

        $data[] = [
            'date' => $date,
            'active_users' => intval($activeUsers),
            'new_users' => intval($newUsers)
        ];
    }

    echo json_encode(['success' => true, 'data' => $data]);
}

function getCategoryStats($db) {
    $sql = "SELECT c.name,
                   COUNT(p.id) as posts,
                   COUNT(cm.id) as comments,
                   (COUNT(p.id) * 15 + COUNT(cm.id) * 3) as views,
                   CASE
                       WHEN COUNT(p.id) = 0 THEN 0
                       ELSE LEAST(100, (COUNT(p.id) * 10 + COUNT(cm.id) * 2))
                   END as activity
            FROM categories c
            LEFT JOIN posts p ON c.id = p.category_id
            LEFT JOIN comments cm ON p.id = cm.post_id
            GROUP BY c.id, c.name
            ORDER BY posts DESC";

    $stats = $db->fetchAll($sql);

    echo json_encode(['success' => true, 'stats' => $stats]);
}

function getPopularPosts($db) {
    $sql = "SELECT p.title, u.nickname as author,
                   (SELECT COUNT(*) FROM comments WHERE post_id = p.id) * 3 + 15 as views
            FROM posts p
            LEFT JOIN users u ON p.user_id = u.id
            ORDER BY views DESC
            LIMIT 5";

    $posts = $db->fetchAll($sql);

    echo json_encode(['success' => true, 'posts' => $posts]);
}

function getActiveUsers($db) {
    $sql = "SELECT u.nickname,
                   COUNT(DISTINCT p.id) as posts,
                   COUNT(DISTINCT c.id) as comments
            FROM users u
            LEFT JOIN posts p ON u.id = p.user_id
            LEFT JOIN comments c ON u.id = c.user_id
            WHERE u.role != 'super_admin'
            GROUP BY u.id, u.nickname
            HAVING (posts > 0 OR comments > 0)
            ORDER BY (posts + comments) DESC
            LIMIT 5";

    $users = $db->fetchAll($sql);

    echo json_encode(['success' => true, 'users' => $users]);
}

function getSystemStatus($db) {
    $status = [];

    try {
        // 检查数据库连接
        $db->fetchOne("SELECT 1");
        $status['database'] = '正常';
    } catch (Exception $e) {
        $status['database'] = '异常';
    }

    // 模拟存储空间检查
    $status['storage'] = '85% 已使用';

    // 最后更新时间
    $status['last_update'] = date('Y-m-d H:i:s');

    echo json_encode(['success' => true, 'status' => $status]);
}

function exportStatistics($db) {
    // 设置CSV文件头
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="community_statistics_' . date('Y-m-d') . '.csv"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');

    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";

    $output = fopen('php://output', 'w');

    // 写入标题
    fputcsv($output, ['社区统计报告 - ' . date('Y-m-d H:i:s')]);
    fputcsv($output, []);

    // 概览统计
    fputcsv($output, ['概览统计']);
    fputcsv($output, ['指标', '数值']);

    $totalPosts = $db->fetchOne("SELECT COUNT(*) as count FROM posts")['count'] ?? 0;
    $totalComments = $db->fetchOne("SELECT COUNT(*) as count FROM comments")['count'] ?? 0;
    $totalUsers = $db->fetchOne("SELECT COUNT(*) as count FROM users")['count'] ?? 0;

    fputcsv($output, ['总帖子数', $totalPosts]);
    fputcsv($output, ['总评论数', $totalComments]);
    fputcsv($output, ['总用户数', $totalUsers]);
    fputcsv($output, []);

    // 分类统计
    fputcsv($output, ['分类统计']);
    fputcsv($output, ['分类名称', '帖子数量', '评论数量']);

    $categoryStats = $db->fetchAll("
        SELECT c.name,
               COUNT(p.id) as posts,
               COUNT(cm.id) as comments
        FROM categories c
        LEFT JOIN posts p ON c.id = p.category_id
        LEFT JOIN comments cm ON p.id = cm.post_id
        GROUP BY c.id, c.name
        ORDER BY posts DESC
    ");

    foreach ($categoryStats as $stat) {
        fputcsv($output, [$stat['name'], $stat['posts'], $stat['comments']]);
    }

    fclose($output);
    exit;
}

function updateUserInfo($db, $input) {
    $userId = intval($input['user_id'] ?? 0);
    $username = trim($input['username'] ?? '');
    $nickname = trim($input['nickname'] ?? '');
    $email = trim($input['email'] ?? '');
    $password = trim($input['password'] ?? '');
    $role = trim($input['role'] ?? 'user');

    if ($userId <= 0 || empty($username) || empty($email)) {
        echo json_encode(['success' => false, 'error' => '参数不完整']);
        return;
    }

    // 检查用户名和邮箱是否重复（排除当前用户）
    $checkSql = "SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?";
    $existing = $db->fetchOne($checkSql, [$username, $email, $userId]);
    if ($existing) {
        echo json_encode(['success' => false, 'error' => '用户名或邮箱已存在']);
        return;
    }

    try {
        $db->beginTransaction();

        // 更新用户基本信息
        $updateFields = ['username = ?', 'email = ?', 'role = ?'];
        $updateValues = [$username, $email, $role];

        if (!empty($password)) {
            $updateFields[] = 'password = ?';
            $updateValues[] = password_hash($password, PASSWORD_DEFAULT);
        }

        $updateValues[] = $userId;
        $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $db->execute($sql, $updateValues);

        // 更新用户资料
        if (!empty($nickname)) {
            $profileSql = "INSERT INTO user_profiles (user_id, full_name) VALUES (?, ?)
                          ON DUPLICATE KEY UPDATE full_name = VALUES(full_name)";
            $db->execute($profileSql, [$userId, $nickname]);
        }

        $db->commit();
        echo json_encode(['success' => true]);
    } catch (Exception $e) {
        $db->rollback();
        echo json_encode(['success' => false, 'error' => '更新失败: ' . $e->getMessage()]);
    }
}



function sendUserNotification($db, $input) {
    $userId = intval($input['user_id'] ?? 0);
    $title = trim($input['title'] ?? '');
    $content = trim($input['content'] ?? '');
    $type = trim($input['type'] ?? 'info');

    if ($userId <= 0 || empty($title) || empty($content)) {
        echo json_encode(['success' => false, 'error' => '参数不完整']);
        return;
    }

    try {
        $sql = "INSERT INTO notifications (user_id, title, content, type, created_at) VALUES (?, ?, ?, ?, NOW())";
        $result = $db->execute($sql, [$userId, $title, $content, $type]);

        if ($result) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'error' => '发送失败']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '发送失败: ' . $e->getMessage()]);
    }
}

function deleteUserAccount($db, $input) {
    $userId = intval($input['user_id'] ?? 0);

    if ($userId <= 0) {
        echo json_encode(['success' => false, 'error' => '无效的用户ID']);
        return;
    }

    try {
        $db->beginTransaction();

        // 删除用户相关数据
        $db->execute("DELETE FROM user_profiles WHERE user_id = ?", [$userId]);
        $db->execute("DELETE FROM notifications WHERE user_id = ?", [$userId]);
        $db->execute("DELETE FROM user_ban_logs WHERE user_id = ?", [$userId]);
        $db->execute("DELETE FROM likes WHERE user_id = ?", [$userId]);
        $db->execute("DELETE FROM comments WHERE user_id = ?", [$userId]);
        $db->execute("DELETE FROM posts WHERE user_id = ?", [$userId]);
        $db->execute("DELETE FROM users WHERE id = ?", [$userId]);

        $db->commit();
        echo json_encode(['success' => true]);
    } catch (Exception $e) {
        $db->rollback();
        echo json_encode(['success' => false, 'error' => '删除失败: ' . $e->getMessage()]);
    }
}

function getNotifications($db) {
    $limit = intval($_GET['limit'] ?? 10);

    try {
        // 获取通知列表
        $sql = "SELECT id, title, content, type, data, is_read, created_at
                FROM notifications
                WHERE user_id IN (SELECT id FROM users WHERE role IN ('admin', 'super_admin'))
                ORDER BY created_at DESC
                LIMIT ?";

        $notifications = $db->fetchAll($sql, [$limit]);

        // 获取未读数量
        $unreadSql = "SELECT COUNT(*) as count
                      FROM notifications
                      WHERE user_id IN (SELECT id FROM users WHERE role IN ('admin', 'super_admin'))
                      AND is_read = 0";

        $unreadResult = $db->fetchOne($unreadSql);
        $unreadCount = $unreadResult['count'] ?? 0;

        echo json_encode([
            'success' => true,
            'notifications' => $notifications,
            'unread_count' => $unreadCount
        ]);
    } catch (Exception $e) {
        // 如果通知表不存在，返回空数据
        echo json_encode([
            'success' => true,
            'notifications' => [],
            'unread_count' => 0
        ]);
    }
}

function markNotificationAsRead($db, $input) {
    $notificationId = intval($input['notification_id'] ?? 0);

    if ($notificationId <= 0) {
        echo json_encode(['success' => false, 'error' => '无效的通知ID']);
        return;
    }

    try {
        $sql = "UPDATE notifications SET is_read = 1 WHERE id = ?";
        $result = $db->execute($sql, [$notificationId]);

        if ($result) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'error' => '标记失败']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '标记失败: ' . $e->getMessage()]);
    }
}

function bulkUpdatePosts($db, $input) {
    try {
        $postIds = $input['ids'] ?? [];
        $status = $input['status'] ?? '';

        if (empty($postIds) || !is_array($postIds)) {
            echo json_encode(['success' => false, 'error' => '无效的帖子ID列表']);
            return;
        }

        $allowedStatuses = ['published', 'draft', 'hidden', 'deleted'];
        if (!in_array($status, $allowedStatuses)) {
            echo json_encode(['success' => false, 'error' => '无效的状态']);
            return;
        }

        $placeholders = str_repeat('?,', count($postIds) - 1) . '?';
        $sql = "UPDATE posts SET status = ? WHERE id IN ($placeholders)";
        $params = array_merge([$status], $postIds);

        $result = $db->execute($sql, $params);

        if ($result) {
            echo json_encode(['success' => true, 'updated' => count($postIds)]);
        } else {
            echo json_encode(['success' => false, 'error' => '批量更新失败']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '批量更新失败: ' . $e->getMessage()]);
    }
}

function bulkDeletePosts($db, $input) {
    try {
        $postIds = $input['ids'] ?? [];

        if (empty($postIds) || !is_array($postIds)) {
            echo json_encode(['success' => false, 'error' => '无效的帖子ID列表']);
            return;
        }

        $placeholders = str_repeat('?,', count($postIds) - 1) . '?';
        $sql = "UPDATE posts SET status = 'deleted' WHERE id IN ($placeholders)";

        $result = $db->execute($sql, $postIds);

        if ($result) {
            echo json_encode(['success' => true, 'deleted' => count($postIds)]);
        } else {
            echo json_encode(['success' => false, 'error' => '批量删除失败']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '批量删除失败: ' . $e->getMessage()]);
    }
}

function createPost($db, $input) {
    try {
        $title = trim($input['title'] ?? '');
        $content = trim($input['content'] ?? '');
        $categoryId = intval($input['category_id'] ?? 0);
        $tags = trim($input['tags'] ?? '');
        $status = $input['status'] ?? 'draft';

        if (empty($title) || empty($content)) {
            echo json_encode(['success' => false, 'error' => '标题和内容不能为空']);
            return;
        }

        if ($categoryId <= 0) {
            echo json_encode(['success' => false, 'error' => '请选择有效的分类']);
            return;
        }

        // 生成摘要
        $excerpt = mb_substr(strip_tags($content), 0, 200);

        // 默认使用管理员用户ID (假设为1)
        $userId = 1;

        $sql = "INSERT INTO posts (user_id, category_id, title, content, excerpt, tags, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

        $result = $db->execute($sql, [$userId, $categoryId, $title, $content, $excerpt, $tags, $status]);

        if ($result) {
            echo json_encode(['success' => true, 'post_id' => $db->lastInsertId()]);
        } else {
            echo json_encode(['success' => false, 'error' => '创建帖子失败']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '创建帖子失败: ' . $e->getMessage()]);
    }
}

// 获取单个评论详情
function getSingleComment($db) {
    try {
        $id = $_GET['id'] ?? 0;

        if (!$id) {
            echo json_encode(['success' => false, 'error' => '评论ID不能为空']);
            return;
        }

        $sql = "SELECT c.*, u.username, u.full_name, up.nickname, up.avatar_url,
                       p.title as post_title, p.id as post_id
                FROM comments c
                LEFT JOIN users u ON c.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                LEFT JOIN posts p ON c.post_id = p.id
                WHERE c.id = ? AND c.status != 'deleted'";

        $comment = $db->fetchOne($sql, [$id]);

        if ($comment) {
            echo json_encode(['success' => true, 'comment' => $comment]);
        } else {
            echo json_encode(['success' => false, 'error' => '评论不存在']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '获取评论失败: ' . $e->getMessage()]);
    }
}

// 更新评论内容
function updateComment($db, $input) {
    try {
        $id = $input['id'] ?? 0;
        $content = trim($input['content'] ?? '');

        if (!$id || empty($content)) {
            echo json_encode(['success' => false, 'error' => '评论ID和内容不能为空']);
            return;
        }

        $sql = "UPDATE comments SET content = ?, updated_at = NOW() WHERE id = ? AND status != 'deleted'";
        $result = $db->execute($sql, [$content, $id]);

        if ($result) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'error' => '更新评论失败']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '更新评论失败: ' . $e->getMessage()]);
    }
}



// 更新评论状态
function updateCommentStatus($db, $input) {
    try {
        $id = $input['id'] ?? 0;
        $status = $input['status'] ?? '';

        if (!$id || !in_array($status, ['published', 'pending', 'spam', 'hidden', 'deleted'])) {
            echo json_encode(['success' => false, 'error' => '评论ID或状态无效']);
            return;
        }

        $sql = "UPDATE comments SET status = ?, updated_at = NOW() WHERE id = ?";
        $result = $db->execute($sql, [$status, $id]);

        if ($result) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'error' => '更新评论状态失败']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '更新评论状态失败: ' . $e->getMessage()]);
    }
}

// 驳回评论举报
function dismissCommentReports($db, $input) {
    try {
        $commentId = $input['comment_id'] ?? 0;

        if (!$commentId) {
            echo json_encode(['success' => false, 'error' => '评论ID不能为空']);
            return;
        }

        // 将该评论的所有举报标记为已处理
        $sql = "UPDATE reports SET status = 'dismissed', updated_at = NOW()
                WHERE target_type = 'comment' AND target_id = ? AND status = 'pending'";
        $result = $db->execute($sql, [$commentId]);

        if ($result) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'error' => '驳回举报失败']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '驳回举报失败: ' . $e->getMessage()]);
    }
}
?>
