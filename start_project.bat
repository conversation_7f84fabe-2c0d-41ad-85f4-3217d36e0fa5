@echo off
echo ================================
echo BitBear System Startup
echo ================================
echo.

set "CURRENT_DIR=%~dp0"

echo Checking for existing startup scripts...

REM Try different startup methods in order of preference
if exist "启动项目.bat" (
    echo Found: 启动项目.bat
    echo Starting with existing startup script...
    start "" "启动项目.bat"
    goto :startup_done
)

if exist "start_server.bat" (
    echo Found: start_server.bat
    echo Starting with existing startup script...
    start "" "start_server.bat"
    goto :startup_done
)

if exist "快速启动后台.bat" (
    echo Found: 快速启动后台.bat
    echo Starting with existing startup script...
    start "" "快速启动后台.bat"
    goto :startup_done
)

if exist "启动PHP后台服务器.bat" (
    echo Found: 启动PHP后台服务器.bat
    echo Starting with existing startup script...
    start "" "启动PHP后台服务器.bat"
    goto :startup_done
)

echo No existing startup scripts found.
echo Attempting automatic startup...
echo.

REM Check for PHP
echo Checking PHP environment...
php --version >nul 2>&1
if %errorlevel% equ 0 (
    echo PHP found! Starting PHP development server...
    echo.
    echo Server will start on: http://localhost:8000
    echo Press Ctrl+C in the server window to stop
    echo.
    start "BitBear PHP Server" cmd /k "echo BitBear System - PHP Development Server && echo Starting on http://localhost:8000 && echo Press Ctrl+C to stop && echo. && php -S localhost:8000 -t . && pause"
    goto :startup_done
)

echo PHP not found. Checking Python environment...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found! Starting Python HTTP server...
    echo.
    echo Server will start on: http://localhost:8000
    echo Press Ctrl+C in the server window to stop
    echo.
    start "BitBear Python Server" cmd /k "echo BitBear System - Python HTTP Server && echo Starting on http://localhost:8000 && echo Press Ctrl+C to stop && echo. && python -m http.server 8000 && pause"
    goto :startup_done
)

echo Neither PHP nor Python found!
echo.
echo Please install one of the following:
echo 1. PHP (recommended for this project)
echo 2. Python (alternative option)
echo.
echo Or check if there are other startup scripts in the project folder.
echo.
pause
exit /b 1

:startup_done
echo.
echo Waiting for server to initialize...
timeout /t 3 >nul

echo Opening browser...
start http://localhost:8000 >nul 2>&1

echo.
echo ================================
echo Startup Complete!
echo ================================
echo.
echo The BitBear System should now be running at:
echo http://localhost:8000
echo.
echo If the browser didn't open automatically,
echo please visit the URL manually.
echo.
echo To stop the server, close the server window
echo or press Ctrl+C in the server terminal.
echo.
pause
