<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类管理功能最终测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .results { margin-top: 20px; max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px; }
        .category-item { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px; border-left: 4px solid #007bff; }
        .test-status { font-weight: bold; font-size: 18px; text-align: center; padding: 15px; margin: 20px 0; border-radius: 8px; }
        .test-passed { background: #d4edda; color: #155724; border: 2px solid #28a745; }
        .test-failed { background: #f8d7da; color: #721c24; border: 2px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 分类管理功能最终测试</h1>
        <p>这个测试将验证分类添加功能是否完全正常工作。</p>
        
        <div class="test-section">
            <h2>测试控制</h2>
            <button onclick="runFullTest()">🚀 运行完整测试</button>
            <button onclick="testAddCategory()">➕ 测试添加分类</button>
            <button onclick="testGetCategories()">📋 获取分类列表</button>
            <button onclick="clearResults()">🗑️ 清空结果</button>
        </div>
        
        <div id="testStatus" class="test-status" style="display: none;"></div>
        
        <div class="results" id="results"></div>
    </div>

    <script>
        let testResults = {
            passed: 0,
            failed: 0,
            total: 0
        };

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
            console.log(message);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('testStatus').style.display = 'none';
            testResults = { passed: 0, failed: 0, total: 0 };
        }

        function updateTestStatus() {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.style.display = 'block';
            
            if (testResults.failed === 0) {
                statusDiv.className = 'test-status test-passed';
                statusDiv.innerHTML = `✅ 所有测试通过！(${testResults.passed}/${testResults.total})`;
            } else {
                statusDiv.className = 'test-status test-failed';
                statusDiv.innerHTML = `❌ 测试失败：${testResults.failed}个失败，${testResults.passed}个通过 (${testResults.total}个总计)`;
            }
        }

        function recordTest(passed, testName) {
            testResults.total++;
            if (passed) {
                testResults.passed++;
                log(`✅ ${testName} - 通过`, 'success');
            } else {
                testResults.failed++;
                log(`❌ ${testName} - 失败`, 'error');
            }
        }

        async function testGetCategories() {
            log('🔍 开始获取分类列表测试...', 'info');
            
            try {
                const response = await fetch('/api/admin-community.php?action=categories');
                const result = await response.json();
                
                if (response.ok && result.success) {
                    recordTest(true, '获取分类列表');
                    log(`📊 获取到 ${result.data.length} 个分类`, 'info');
                    
                    result.data.forEach((cat, index) => {
                        const categoryDiv = document.createElement('div');
                        categoryDiv.className = 'category-item';
                        categoryDiv.innerHTML = `
                            <strong>${cat.name}</strong> (${cat.slug || '无slug'}) 
                            <br>描述: ${cat.description || '无描述'}
                            <br>颜色: ${cat.color} | 排序: ${cat.sort_order} | 状态: ${cat.is_active ? '激活' : '禁用'}
                        `;
                        document.getElementById('results').appendChild(categoryDiv);
                    });
                    
                    return result.data;
                } else {
                    recordTest(false, '获取分类列表');
                    log(`获取分类失败: ${result.error || '未知错误'}`, 'error');
                    return null;
                }
            } catch (error) {
                recordTest(false, '获取分类列表');
                log(`请求失败: ${error.message}`, 'error');
                return null;
            }
        }

        async function testAddCategory() {
            log('➕ 开始添加分类测试...', 'info');
            
            const testData = {
                action: 'add_category',
                name: '最终测试分类_' + Date.now(),
                description: '这是最终测试创建的分类，用于验证功能完整性',
                color: '#28a745',
                icon: '🧪',
                sort_order: 100
            };

            log(`📝 测试数据: ${testData.name}`, 'info');

            try {
                const response = await fetch('/api/admin-community.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                log(`🌐 HTTP状态: ${response.status} ${response.statusText}`, response.ok ? 'info' : 'error');
                
                const responseText = await response.text();
                let result;
                
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    recordTest(false, '添加分类 - JSON解析');
                    log(`JSON解析失败: ${e.message}`, 'error');
                    log(`原始响应: ${responseText}`, 'error');
                    return null;
                }
                
                if (response.ok && result.success) {
                    recordTest(true, '添加分类');
                    log(`🎉 分类添加成功！ID: ${result.id}, Slug: ${result.slug || '未返回'}`, 'success');
                    return result;
                } else {
                    recordTest(false, '添加分类');
                    log(`添加分类失败: ${result.error || '未知错误'}`, 'error');
                    return null;
                }
            } catch (error) {
                recordTest(false, '添加分类');
                log(`请求失败: ${error.message}`, 'error');
                return null;
            }
        }

        async function runFullTest() {
            log('🚀 开始运行完整的分类管理功能测试...', 'info');
            clearResults();
            
            // 测试1: 获取初始分类列表
            log('=== 测试1: 获取初始分类列表 ===', 'info');
            const initialCategories = await testGetCategories();
            const initialCount = initialCategories ? initialCategories.length : 0;
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试2: 添加新分类
            log('=== 测试2: 添加新分类 ===', 'info');
            const addResult = await testAddCategory();
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试3: 验证分类是否成功添加
            log('=== 测试3: 验证分类添加结果 ===', 'info');
            const finalCategories = await testGetCategories();
            const finalCount = finalCategories ? finalCategories.length : 0;
            
            if (addResult && finalCount > initialCount) {
                recordTest(true, '分类数量验证');
                log(`✅ 分类数量从 ${initialCount} 增加到 ${finalCount}`, 'success');
            } else {
                recordTest(false, '分类数量验证');
                log(`❌ 分类数量验证失败: 初始${initialCount}, 最终${finalCount}`, 'error');
            }
            
            // 更新测试状态
            updateTestStatus();
            
            log('=== 测试完成 ===', 'info');
            
            if (testResults.failed === 0) {
                log('🎉 恭喜！分类管理功能测试全部通过！', 'success');
                log('✅ 数据库slug字段问题已修复', 'success');
                log('✅ API能够正确处理分类添加请求', 'success');
                log('✅ 前端和后端通信正常', 'success');
            } else {
                log('⚠️ 部分测试失败，请检查相关功能', 'error');
            }
        }

        // 页面加载时显示欢迎信息
        window.onload = function() {
            log('🎯 分类管理功能测试页面已加载', 'info');
            log('👆 点击"运行完整测试"开始验证功能', 'info');
        };
    </script>
</body>
</html>
