<?php
// 设置session配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0);
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.use_strict_mode', 1);

session_start();
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'includes/time_helper.php';

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

$postId = intval($_GET['id'] ?? 0);
if (!$postId) {
    header('Location: community.php');
    exit;
}

$error = '';
$success = '';

try {
    $db = db();
    
    // 获取帖子详情
    $sql = "SELECT p.*, u.username, u.full_name, up.nickname, up.avatar_url, up.signature,
                   pc.name as category_name, pc.slug as category_slug, pc.color as category_color
            FROM posts p
            LEFT JOIN users u ON p.user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN post_categories pc ON p.category_id = pc.id
            WHERE p.id = ? AND p.status = 'published'";
    
    $post = $db->fetchOne($sql, [$postId]);
    
    if (!$post) {
        header('Location: community.php');
        exit;
    }
    
    // 增加浏览量
    $db->execute("UPDATE posts SET view_count = view_count + 1 WHERE id = ?", [$postId]);
    $post['view_count']++;
    
    // 获取评论列表（按楼层排序）
    $commentsSql = "SELECT c.*, u.username, u.full_name, up.nickname, up.avatar_url, up.signature
                    FROM comments c
                    LEFT JOIN users u ON c.user_id = u.id
                    LEFT JOIN user_profiles up ON u.id = up.user_id
                    WHERE c.post_id = ? AND c.status = 'published'
                    ORDER BY c.floor_number ASC, c.created_at ASC";
    
    $comments = $db->fetchAll($commentsSql, [$postId]);
    
    // 构建评论树结构
    $commentTree = buildCommentTree($comments);
    
    // 检查用户是否已点赞
    $userLiked = false;
    $userDisliked = false;
    if ($currentUser) {
        $likeSql = "SELECT type FROM likes WHERE user_id = ? AND target_type = 'post' AND target_id = ?";
        $userLike = $db->fetchOne($likeSql, [$currentUser['id'], $postId]);
        if ($userLike) {
            $userLiked = $userLike['type'] === 'like';
            $userDisliked = $userLike['type'] === 'dislike';
        }
    }

    // 检查用户是否已收藏
    $userBookmarked = false;
    if ($currentUser) {
        $bookmarkSql = "SELECT id FROM bookmarks WHERE user_id = ? AND post_id = ?";
        $userBookmark = $db->fetchOne($bookmarkSql, [$currentUser['id'], $postId]);
        $userBookmarked = (bool)$userBookmark;
    }
    
} catch (Exception $e) {
    error_log("帖子详情页错误: " . $e->getMessage());
    header('Location: community.php');
    exit;
}

// 处理评论提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!$currentUser) {
        $error = '请先登录后再评论';
    } else {
        try {
            $action = $_POST['action'];
            
            if ($action === 'comment') {
                $content = trim($_POST['content'] ?? '');
                $parentId = intval($_POST['parent_id'] ?? 0) ?: null;
                
                if (empty($content)) {
                    throw new Exception('请输入评论内容');
                }
                
                // 计算楼层号和层级
                $floorNumber = 1;
                $level = 0;
                $path = '';
                
                if ($parentId) {
                    // 获取父评论信息
                    $parentComment = $db->fetchOne("SELECT * FROM comments WHERE id = ?", [$parentId]);
                    if ($parentComment) {
                        $level = $parentComment['level'] + 1;
                        $path = $parentComment['path'] . $parentId . '/';
                    }
                } else {
                    // 获取最大楼层号
                    $maxFloor = $db->fetchOne("SELECT MAX(floor_number) as max_floor FROM comments WHERE post_id = ? AND parent_id IS NULL", [$postId]);
                    $floorNumber = ($maxFloor['max_floor'] ?? 0) + 1;
                }
                
                // 插入评论
                $insertSql = "INSERT INTO comments (post_id, user_id, parent_id, content, floor_number, level, path, ip_address, user_agent, created_at) 
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                
                $params = [
                    $postId,
                    $currentUser['id'],
                    $parentId,
                    $content,
                    $floorNumber,
                    $level,
                    $path,
                    $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '',
                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                ];
                
                $db->execute($insertSql, $params);
                
                // 更新帖子评论数
                $db->execute("UPDATE posts SET comment_count = comment_count + 1 WHERE id = ?", [$postId]);
                
                // 更新父评论回复数
                if ($parentId) {
                    $db->execute("UPDATE comments SET reply_count = reply_count + 1 WHERE id = ?", [$parentId]);
                }
                
                // 更新用户评论数
                $db->execute("UPDATE user_profiles SET comment_count = comment_count + 1 WHERE user_id = ?", [$currentUser['id']]);
                
                $success = '评论发布成功！';
                
                // 重新加载页面
                header('Location: ' . $_SERVER['REQUEST_URI']);
                exit;
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// 构建评论树结构
function buildCommentTree($comments) {
    $tree = [];
    $lookup = [];
    
    // 创建查找表
    foreach ($comments as $comment) {
        $comment['children'] = [];
        $lookup[$comment['id']] = $comment;
    }
    
    // 构建树结构
    foreach ($lookup as $comment) {
        if ($comment['parent_id']) {
            if (isset($lookup[$comment['parent_id']])) {
                $lookup[$comment['parent_id']]['children'][] = &$lookup[$comment['id']];
            }
        } else {
            $tree[] = &$lookup[$comment['id']];
        }
    }
    
    return $tree;
}

// 辅助函数已移至 includes/time_helper.php

function renderComments($comments, $level = 0) {
    if (empty($comments)) return '';
    
    $html = '';
    foreach ($comments as $comment) {
        $html .= renderComment($comment, $level);
    }
    return $html;
}

function renderComment($comment, $level = 0) {
    global $currentUser;
    
    $authorName = $comment['nickname'] ?? $comment['full_name'] ?? $comment['username'];
    $avatar = $comment['avatar_url'] ?? 'assets/images/default-avatar.png';
    $canDelete = $currentUser && ($currentUser['id'] == $comment['user_id'] || in_array($currentUser['role_code'] ?? '', ['admin', 'super_admin']));
    
    $html = '<div class="comment-item" id="comment-' . $comment['id'] . '" data-comment-id="' . $comment['id'] . '" data-user-id="' . $comment['user_id'] . '" style="margin-left: ' . ($level * 40) . 'px;">';
    $html .= '<div class="comment-content">';
    $html .= '<div class="comment-header">';
    $html .= '<div class="comment-author">';
    $html .= '<a href="user-profile.php?id=' . $comment['user_id'] . '" class="comment-avatar-link">';
    $html .= '<img src="' . escapeHtml($avatar) . '" alt="头像" class="comment-avatar">';
    $html .= '</a>';
    $html .= '<div class="author-info">';
    $html .= '<a href="user-profile.php?id=' . $comment['user_id'] . '" class="author-name-link">';
    $html .= '<span class="author-name">' . escapeHtml($authorName) . '</span>';
    $html .= '</a>';
    if ($comment['signature']) {
        $html .= '<span class="author-signature">' . escapeHtml($comment['signature']) . '</span>';
    }
    $html .= '</div>';
    $html .= '</div>';
    $html .= '<div class="comment-meta">';
    if (!$comment['parent_id']) {
        $html .= '<span class="floor-number"><i class="fas fa-hashtag"></i>' . $comment['floor_number'] . '楼</span>';
    } else {
        $html .= '<span class="floor-number"><i class="fas fa-reply"></i>回复</span>';
    }
    $html .= '<span class="comment-time" datetime="' . $comment['created_at'] . '"><i class="fas fa-clock"></i>' . timeAgo($comment['created_at']) . '</span>';
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '<div class="comment-body">';
    $html .= '<div class="comment-text">' . nl2br(escapeHtml($comment['content'])) . '</div>';
    $html .= '</div>';
    
    $html .= '<div class="comment-actions">';
    $html .= '<button class="action-btn like-btn" data-comment-id="' . $comment['id'] . '">';
    $html .= '<i class="fas fa-thumbs-up"></i> ' . formatNumber($comment['like_count']);
    $html .= '</button>';
    $html .= '<button class="action-btn dislike-btn" data-comment-id="' . $comment['id'] . '">';
    $html .= '<i class="fas fa-thumbs-down"></i> ' . formatNumber($comment['dislike_count']);
    $html .= '</button>';
    if ($currentUser) {
        $html .= '<button class="action-btn reply-btn" data-comment-id="' . $comment['id'] . '">';
        $html .= '<i class="fas fa-reply"></i> 回复';
        $html .= '</button>';
    }
    if ($canDelete) {
        $html .= '<button class="action-btn delete-btn" data-comment-id="' . $comment['id'] . '">';
        $html .= '<i class="fas fa-trash"></i> 删除';
        $html .= '</button>';
    }
    $html .= '</div>';
    
    $html .= '</div>';
    
    // 渲染子评论
    if (!empty($comment['children'])) {
        $html .= '<div class="comment-replies">';
        $html .= renderComments($comment['children'], $level + 1);
        $html .= '</div>';
    }
    
    $html .= '</div>';
    
    return $html;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escapeHtml($post['title']); ?> - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/community.css">
    <link rel="stylesheet" href="assets/css/community-post-detail.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.php">
                    <img src="assets/images/logo.png" alt="比特熊智慧系统" class="logo">
                    <span>比特熊智慧系统</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="index.php" class="nav-link">首页</a>
                <a href="community.php" class="nav-link">社区</a>
                <a href="#" class="nav-link">课程</a>
                <a href="#" class="nav-link">文档</a>
            </div>
            
            <div class="nav-actions">
                <?php if ($currentUser): ?>
                    <a href="community-post.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 发帖
                    </a>
                    <a href="bookmarks.php" class="nav-link" title="我的收藏">
                        <i class="fas fa-bookmark"></i>
                    </a>
                    <div class="user-menu">
                        <img src="<?php echo $currentUser['avatar'] ?? 'assets/images/default-avatar.png'; ?>"
                             alt="头像" class="user-avatar">
                        <span><?php echo escapeHtml($currentUser['username']); ?></span>
                    </div>
                <?php else: ?>
                    <a href="admin-login.php" class="btn btn-outline">登录</a>
                    <a href="register.php" class="btn btn-primary">注册</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 返回按钮 -->
            <div class="breadcrumb">
                <a href="community.php" class="breadcrumb-link">
                    <i class="fas fa-arrow-left"></i> 返回社区
                </a>
            </div>

            <!-- 帖子内容 -->
            <article class="post-detail">
                <header class="post-header">
                    <div class="post-meta">
                        <?php if ($post['category_name']): ?>
                        <span class="post-category" style="--category-color: <?php echo $post['category_color']; ?>">
                            <?php echo escapeHtml($post['category_name']); ?>
                        </span>
                        <?php endif; ?>
                        
                        <?php if ($post['is_pinned']): ?>
                        <span class="pin-badge">
                            <i class="fas fa-thumbtack"></i> 置顶
                        </span>
                        <?php endif; ?>
                    </div>
                    
                    <h1 class="post-title"><?php echo escapeHtml($post['title']); ?></h1>
                    
                    <div class="post-info">
                        <div class="author-section">
                            <a href="user-profile.php?id=<?php echo $post['user_id']; ?>" class="author-avatar-link">
                                <img src="<?php echo $post['avatar_url'] ?? 'assets/images/default-avatar.png'; ?>"
                                     alt="头像" class="author-avatar">
                            </a>
                            <div class="author-details">
                                <a href="user-profile.php?id=<?php echo $post['user_id']; ?>" class="author-name-link">
                                    <span class="author-name">
                                        <?php echo escapeHtml($post['nickname'] ?? $post['full_name'] ?? $post['username']); ?>
                                    </span>
                                </a>
                                <div class="post-meta-info">
                                    <span class="post-time">发布于 <?php echo timeAgo($post['created_at']); ?></span>
                                    <?php if ($post['updated_at'] !== $post['created_at']): ?>
                                    <span class="post-updated">编辑于 <?php echo timeAgo($post['updated_at']); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="post-stats">
                            <div class="stat-item">
                                <i class="fas fa-eye"></i>
                                <span><?php echo formatNumber($post['view_count']); ?></span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-comment"></i>
                                <span><?php echo formatNumber($post['comment_count']); ?></span>
                            </div>
                        </div>
                    </div>
                </header>
                
                <div class="post-content">
                    <?php if ($post['featured_image']): ?>
                    <div class="featured-image">
                        <img src="<?php echo $post['featured_image']; ?>" alt="特色图片">
                    </div>
                    <?php endif; ?>
                    
                    <div class="post-body">
                        <?php echo $post['content']; ?>
                    </div>
                </div>
                
                <div class="post-actions">
                    <div class="action-buttons">
                        <button class="action-btn like-btn <?php echo $userLiked ? 'active' : ''; ?>" 
                                data-post-id="<?php echo $post['id']; ?>" data-action="like">
                            <i class="fas fa-heart"></i>
                            <span><?php echo formatNumber($post['like_count']); ?></span>
                        </button>
                        
                        <button class="action-btn dislike-btn <?php echo $userDisliked ? 'active' : ''; ?>" 
                                data-post-id="<?php echo $post['id']; ?>" data-action="dislike">
                            <i class="fas fa-thumbs-down"></i>
                            <span><?php echo formatNumber($post['dislike_count']); ?></span>
                        </button>
                        
                        <button class="action-btn share-btn" data-post-id="<?php echo $post['id']; ?>">
                            <i class="fas fa-share"></i>
                            <span>分享</span>
                        </button>
                        
                        <button class="action-btn bookmark-btn <?php echo $userBookmarked ? 'active' : ''; ?>"
                                data-post-id="<?php echo $post['id']; ?>">
                            <i class="fas fa-bookmark"></i>
                            <span>收藏</span>
                        </button>

                        <?php if ($currentUser && ($currentUser['id'] == $post['user_id'] || in_array($currentUser['role_code'] ?? '', ['admin', 'super_admin']))): ?>
                        <a href="community-post-edit.php?id=<?php echo $post['id']; ?>" class="action-btn edit-btn">
                            <i class="fas fa-edit"></i>
                            <span>编辑</span>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </article>

            <!-- 评论区 -->
            <section class="comments-section">
                <div class="comments-header">
                    <h2 class="comments-title">
                        <i class="fas fa-comments"></i>
                        评论 (<?php echo count($comments); ?>)
                    </h2>
                    
                    <div class="comments-sort">
                        <select id="commentSort">
                            <option value="floor">按楼层排序</option>
                            <option value="time">按时间排序</option>
                            <option value="likes">按点赞排序</option>
                        </select>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo escapeHtml($error); ?>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo escapeHtml($success); ?>
                </div>
                <?php endif; ?>

                <!-- 评论表单 -->
                <?php if ($currentUser): ?>
                <div class="comment-form-container">
                    <form method="POST" class="comment-form" id="commentForm">
                        <input type="hidden" name="action" value="comment">
                        <input type="hidden" name="parent_id" value="" id="parentId">
                        
                        <div class="form-header">
                            <img src="<?php echo $currentUser['avatar'] ?? 'assets/images/default-avatar.png'; ?>" 
                                 alt="头像" class="user-avatar">
                            <div class="form-info">
                                <span class="user-name"><?php echo escapeHtml($currentUser['username']); ?></span>
                                <span class="form-label" id="formLabel">发表评论</span>
                            </div>
                        </div>
                        
                        <div class="form-body">
                            <textarea name="content" 
                                      id="commentContent" 
                                      class="comment-textarea" 
                                      placeholder="写下你的想法..."
                                      required></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn btn-outline" id="cancelReply" style="display: none;">
                                取消回复
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                发布评论
                            </button>
                        </div>
                    </form>
                </div>
                <?php else: ?>
                <div class="login-prompt">
                    <p>请 <a href="admin-login.php?redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>">登录</a> 后发表评论</p>
                </div>
                <?php endif; ?>

                <!-- 评论列表 -->
                <div class="comments-list comments-timeline">
                    <?php if (empty($commentTree)): ?>
                    <div class="empty-comments">
                        <div class="empty-icon">
                            <i class="fas fa-comment-slash"></i>
                        </div>
                        <p>暂无评论，来发表第一个评论吧！</p>
                    </div>
                    <?php else: ?>
                        <?php echo renderComments($commentTree); ?>
                    <?php endif; ?>
                </div>
            </section>
        </div>
    </main>

    <script src="assets/js/community-post-detail.js"></script>
    <script>
        // 调试：添加网络请求监控
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            console.log('🌐 Fetch请求:', args[0], args[1]);
            return originalFetch.apply(this, args)
                .then(response => {
                    console.log('📥 Fetch响应:', response.status, response.statusText);
                    return response;
                })
                .catch(error => {
                    console.error('❌ Fetch错误:', error);
                    throw error;
                });
        };

        // 为不同用户生成不同的圆圈颜色
        document.addEventListener('DOMContentLoaded', function() {
            const colors = [
                '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
                '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43',
                '#a55eea', '#26de81', '#fc5c65', '#fed330', '#fd79a8',
                '#fdcb6e', '#6c5ce7', '#74b9ff', '#00b894', '#e17055'
            ];

            const commentItems = document.querySelectorAll('.comment-item[data-user-id]');
            const userColors = new Map();

            commentItems.forEach(item => {
                const userId = item.getAttribute('data-user-id');

                // 如果这个用户还没有分配颜色，就分配一个
                if (!userColors.has(userId)) {
                    const colorIndex = parseInt(userId) % colors.length;
                    userColors.set(userId, colors[colorIndex]);
                }

                // 设置这个评论项的颜色
                const color = userColors.get(userId);
                item.style.setProperty('--user-color', color);
            });
        });

        // 处理评论定位
        document.addEventListener('DOMContentLoaded', function() {
            // 检查URL中是否有评论锚点
            const hash = window.location.hash;
            if (hash && hash.startsWith('#comment-')) {
                const commentElement = document.querySelector(hash);
                if (commentElement) {
                    // 延迟滚动，确保页面完全加载
                    setTimeout(() => {
                        commentElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });

                        // 高亮显示目标评论
                        commentElement.style.backgroundColor = '#fff3cd';
                        commentElement.style.border = '2px solid #ffc107';
                        commentElement.style.borderRadius = '8px';

                        // 3秒后移除高亮
                        setTimeout(() => {
                            commentElement.style.backgroundColor = '';
                            commentElement.style.border = '';
                            commentElement.style.borderRadius = '';
                        }, 3000);
                    }, 500);
                }
            }
        });
    </script>
    <style>
        .comment-item::before {
            background: var(--user-color, #4a90e2) !important;
        }
    </style>
</body>
</html>
