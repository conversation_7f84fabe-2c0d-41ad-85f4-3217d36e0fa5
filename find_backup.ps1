# BitBear System Backup Finder
Write-Host "================================" -ForegroundColor Green
Write-Host "BitBear System Backup Finder" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

# Search for backup folders
$searchPaths = @("C:\", "D:\", "E:\", "F:\", "G:\")
$backupFound = $false
$backupPaths = @()

Write-Host "Searching for backup folders..." -ForegroundColor Yellow

foreach ($drive in $searchPaths) {
    if (Test-Path $drive) {
        Write-Host "Searching in $drive..." -ForegroundColor Cyan
        
        # Search for folders containing "比特熊" and "备份"
        try {
            $folders = Get-ChildItem -Path $drive -Directory -Recurse -ErrorAction SilentlyContinue | 
                       Where-Object { $_.Name -like "*比特熊*" -and $_.Name -like "*备份*" }
            
            foreach ($folder in $folders) {
                Write-Host "Found: $($folder.FullName)" -ForegroundColor Green
                $backupPaths += $folder.FullName
                $backupFound = $true
            }
        }
        catch {
            Write-Host "Error searching $drive : $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

if (-not $backupFound) {
    Write-Host "No backup folders found automatically." -ForegroundColor Red
    Write-Host "Searching for any folder containing '比特熊'..." -ForegroundColor Yellow
    
    foreach ($drive in $searchPaths) {
        if (Test-Path $drive) {
            try {
                $folders = Get-ChildItem -Path $drive -Directory -ErrorAction SilentlyContinue | 
                           Where-Object { $_.Name -like "*比特熊*" }
                
                foreach ($folder in $folders) {
                    Write-Host "Found BitBear folder: $($folder.FullName)" -ForegroundColor Cyan
                    
                    # Check if it contains backup subfolders
                    $subfolders = Get-ChildItem -Path $folder.FullName -Directory -ErrorAction SilentlyContinue |
                                  Where-Object { $_.Name -like "*备份*" -or $_.Name -like "*backup*" }
                    
                    foreach ($subfolder in $subfolders) {
                        Write-Host "  -> Backup subfolder: $($subfolder.FullName)" -ForegroundColor Green
                        $backupPaths += $subfolder.FullName
                        $backupFound = $true
                    }
                }
            }
            catch {
                Write-Host "Error searching $drive : $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

Write-Host ""
Write-Host "================================" -ForegroundColor Green

if ($backupFound) {
    Write-Host "Found backup locations:" -ForegroundColor Green
    for ($i = 0; $i -lt $backupPaths.Count; $i++) {
        Write-Host "[$($i + 1)] $($backupPaths[$i])" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "Please select a backup to restore from (enter number):" -ForegroundColor Yellow
    $selection = Read-Host "Selection"
    
    try {
        $selectedIndex = [int]$selection - 1
        if ($selectedIndex -ge 0 -and $selectedIndex -lt $backupPaths.Count) {
            $selectedBackup = $backupPaths[$selectedIndex]
            Write-Host "Selected: $selectedBackup" -ForegroundColor Green
            
            # Save selection to file for batch script to use
            $selectedBackup | Out-File -FilePath "selected_backup.txt" -Encoding UTF8
            Write-Host "Backup path saved to selected_backup.txt" -ForegroundColor Green
        }
        else {
            Write-Host "Invalid selection!" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "Invalid input!" -ForegroundColor Red
    }
}
else {
    Write-Host "No backup folders found!" -ForegroundColor Red
    Write-Host "Please check if:" -ForegroundColor Yellow
    Write-Host "1. The backup folder exists" -ForegroundColor White
    Write-Host "2. The backup folder is accessible" -ForegroundColor White
    Write-Host "3. The backup folder name contains '比特熊' and '备份'" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
